<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Spectre.Console" Version="0.49.1" />
    <PackageReference Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageReference Include="NetEscapades.Configuration.Yaml" Version="3.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../VideoContentAnalyzer.Core/VideoContentAnalyzer.Core.csproj" />
    <ProjectReference Include="../VideoContentAnalyzer.Infrastructure/VideoContentAnalyzer.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.yaml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>