using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Core.Interfaces;

public interface IAIAnalysisService
{
    Task<SceneDescription> AnalyzeFrameAsync(string framePath, CancellationToken cancellationToken = default);
    Task<(SceneDescription result, TimeSpan duration)> AnalyzeFrameWithTimingAsync(string framePath, CancellationToken cancellationToken = default);
    Task<(SceneDescription result, TimeSpan duration)> AnalyzeFrameWithTimingAsync(string framePath, YouTubeVideoInfo? youtubeMetadata, CancellationToken cancellationToken = default);
    Task<List<DetectedText>> ExtractTextFromFrameAsync(string framePath, CancellationToken cancellationToken = default);
    Task<List<PlaceInfo>> ExtractPlaceInfoAsync(string framePath, CancellationToken cancellationToken = default);
    Task<List<SceneDescription>> AnalyzeFramesBatchAsync(IEnumerable<string> framePaths, CancellationToken cancellationToken = default);
    Task<SubtitleAnalysis> AnalyzeSubtitleSegmentAsync(string text, CancellationToken cancellationToken = default);
    Task<(string summaryText, string subtitleContent, List<string> subtitleSegments)> GenerateVideoSummaryAsync(List<FrameAnalysis> frameAnalyses, List<SubtitleSegment> subtitles, CancellationToken cancellationToken = default);
    Task<(string summaryText, string subtitleContent, List<string> subtitleSegments)> GenerateVideoSummaryAsync(List<FrameAnalysis> frameAnalyses, List<SubtitleSegment> subtitles, YouTubeVideoInfo? youtubeMetadata, CancellationToken cancellationToken = default);
    Task<List<string>> ConfirmRestaurantNamesAsync(List<string> restaurantNames, CancellationToken cancellationToken = default);
    Task<List<string>> ExtractRestaurantNamesFromReportAsync(string reportText, CancellationToken cancellationToken = default);
}

public interface IOpenRouterClient
{
    Task<string> SendTextRequestAsync(string prompt, CancellationToken cancellationToken = default);
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);
}

public interface ILMStudioClient
{
    Task<(string result, TimeSpan duration)> SendVisionRequestAsync(string imagePath, string prompt, CancellationToken cancellationToken = default);
    Task<string> SendTextRequestAsync(string prompt, CancellationToken cancellationToken = default);
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);
}

public interface IYouTubeDownloadService
{
    Task<YouTubeDownloadResult> DownloadVideoAsync(string urlOrId, CancellationToken cancellationToken = default);
    Task<YouTubeVideoInfo> GetVideoInfoAsync(string urlOrId, CancellationToken cancellationToken = default);
    Task<bool> IsValidYouTubeUrlAsync(string urlOrId);
}

public interface IYouTubeApiService
{
    Task<YouTubeVideoInfo?> GetVideoDetailsAsync(string videoId);
    Task<string> GetCategoryNameAsync(string categoryId);
    Task<Dictionary<string, Core.Models.VideoCategory>> LoadVideoCategoriesAsync();
    string MapVideoLanguageToSubtitle(string? videoLanguage);
    string GetPreferredSubtitleLanguage(YouTubeVideoInfo videoInfo);
    bool IsAvailable();
    
    // 新增的元資料收集方法
    Task<YouTubeMetadataCollectionResult> CollectVideoMetadataAsync(string videoId, CancellationToken cancellationToken = default);
    Task<YouTubeChannelInfo?> GetChannelDetailsAsync(string channelId, CancellationToken cancellationToken = default);
    Task<List<YouTubeComment>> GetVideoCommentsAsync(string videoId, int maxResults = 20, CancellationToken cancellationToken = default);
    Task<List<SubtitleLanguageInfo>> GetAvailableSubtitlesAsync(string videoId, CancellationToken cancellationToken = default);
    Task<List<string>> ExtractKeywordsFromVideoAsync(YouTubeVideoInfo videoInfo, CancellationToken cancellationToken = default);
}

public interface IPlaceDetectionService
{
    Task<List<PlaceReference>> DetectPlacesFromTextAsync(string text, string source = "unknown", CancellationToken cancellationToken = default);
    Task<List<PlaceReference>> DetectPlacesFromVideoInfoAsync(YouTubeVideoInfo videoInfo, CancellationToken cancellationToken = default);
    Task<PlaceReference?> GetPlaceDetailsAsync(string placeId, CancellationToken cancellationToken = default);
    Task<List<PlaceReference>> SearchNearbyPlacesAsync(double latitude, double longitude, int radiusMeters = 5000, CancellationToken cancellationToken = default);
    bool IsEnabled { get; }
}
