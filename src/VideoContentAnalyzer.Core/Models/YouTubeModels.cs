namespace VideoContentAnalyzer.Core.Models;

public class YouTubeDownloadResult
{
    public bool Success { get; set; }
    public required string VideoPath { get; set; }
    public string? ErrorMessage { get; set; }
    public YouTubeVideoInfo? VideoInfo { get; set; }
    public TimeSpan DownloadDuration { get; set; }
    public long FileSizeBytes { get; set; }
    public List<string> SubtitlePaths { get; set; } = [];
    public string? PreferredSubtitlePath { get; set; }
}

public class YouTubeVideoInfo
{
    public required string Id { get; set; }
    public required string Title { get; set; }
    public required string Url { get; set; }
    public string? Description { get; set; }
    public string? Channel { get; set; }
    public string? ChannelId { get; set; }
    public TimeSpan Duration { get; set; }
    public string? ThumbnailUrl { get; set; }
    public DateTime? UploadDate { get; set; }
    public long? ViewCount { get; set; }
    public long? LikeCount { get; set; }
    public long? CommentCount { get; set; }
    public string? CategoryId { get; set; }
    public string? CategoryName { get; set; }
    public string? DefaultLanguage { get; set; }
    public string? DefaultAudioLanguage { get; set; }
    public bool HasCaptions { get; set; }
    public bool HasAutoGeneratedCaptions { get; set; }
    public List<SubtitleLanguageInfo> AvailableSubtitles { get; set; } = [];
    public List<YouTubeFormat> AvailableFormats { get; set; } = [];
    
    // 新增的擴展元資料
    public YouTubeChannelInfo? ChannelInfo { get; set; }
    public List<string> Tags { get; set; } = [];
    public double? AverageRating { get; set; }
    public long? DislikeCount { get; set; }
    public long? SubscriberCount { get; set; }
    public string? LiveStatus { get; set; } // live, upcoming, completed
    public DateTime? LiveStartTime { get; set; }
    public string? License { get; set; }
    public string? OriginalUrl { get; set; }
    public List<YouTubeComment> TopComments { get; set; } = [];
    public List<PlaceReference> DetectedPlaces { get; set; } = []; // 從標題和描述中檢測到的地點
    public List<string> ExtractedKeywords { get; set; } = []; // 關鍵字萃取
    public YouTubeVideoStatistics? Statistics { get; set; }
    public DateTime CollectedAt { get; set; } = DateTime.UtcNow;
}

public class YouTubeFormat
{
    public required string FormatId { get; set; }
    public required string Extension { get; set; }
    public string? Resolution { get; set; }
    public long? FileSizeBytes { get; set; }
    public string? VideoCodec { get; set; }
    public string? AudioCodec { get; set; }
    public int? Fps { get; set; }
}

public class YouTubeDownloadOptions
{
    public string OutputDirectory { get; set; } = "./data/downloads";
    public string PreferredQuality { get; set; } = "720p"; // 720p, 1080p, best, worst
    public string PreferredFormat { get; set; } = "mp4"; // mp4, webm, mkv
    public bool KeepOriginalFilename { get; set; } = false;
    public string FilenameTemplate { get; set; } = "%(title)s.%(ext)s";
    public bool DownloadSubtitles { get; set; } = true;
    public string SubtitleLanguages { get; set; } = "zh,en,ja,ko"; // 中文、英文、日文、韓文
    public bool EmbedSubtitles { get; set; } = false;
    public int TimeoutSeconds { get; set; } = 300;
    public bool CleanupAfterAnalysis { get; set; } = true; // 分析完成後清理下載檔案
}

public class VideoCategory
{
    public required string Id { get; set; }
    public required string Title { get; set; }
    public bool Assignable { get; set; }
}

public class SubtitleLanguageInfo
{
    public required string LanguageCode { get; set; }
    public required string LanguageName { get; set; }
    public bool IsAutoGenerated { get; set; }
    public string? Url { get; set; }
}

public class YouTubeApiOptions
{
    public string ApiKey { get; set; } = string.Empty;
    public string ApplicationName { get; set; } = "VideoContentAnalyzer";
    public string DefaultRegionCode { get; set; } = "TW";
    public List<string> PreferredSubtitleLanguages { get; set; } = ["zh-TW", "zh-CN", "ja", "ko", "en"];
}

// 新增的擴展模型類
public class YouTubeChannelInfo
{
    public required string Id { get; set; }
    public required string Title { get; set; }
    public string? Description { get; set; }
    public long? SubscriberCount { get; set; }
    public long? VideoCount { get; set; }
    public long? ViewCount { get; set; }
    public DateTime? CreatedAt { get; set; }
    public string? Country { get; set; }
    public string? ThumbnailUrl { get; set; }
    public string? BannerUrl { get; set; }
    public List<string> Keywords { get; set; } = [];
    public string Url => $"https://www.youtube.com/channel/{Id}";
}

public class YouTubeComment
{
    public required string Id { get; set; }
    public required string Text { get; set; }
    public required string AuthorDisplayName { get; set; }
    public string? AuthorProfileImageUrl { get; set; }
    public string? AuthorChannelId { get; set; }
    public long LikeCount { get; set; }
    public DateTime PublishedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? ParentId { get; set; } // 用於回覆評論
    public bool IsReply => !string.IsNullOrEmpty(ParentId);
}

public class PlaceReference
{
    public required string Name { get; set; }
    public string? FormattedAddress { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string? PlaceId { get; set; } // Google Places ID
    public List<string> Types { get; set; } = []; // restaurant, tourist_attraction, etc.
    public double? Rating { get; set; }
    public int? UserRatingsTotal { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Website { get; set; }
    public string? PriceLevel { get; set; } // 免費, 便宜, 中等, 昂貴, 非常昂貴
    public bool? OpenNow { get; set; }
    public List<string> OpeningHours { get; set; } = [];
    public string? DetectionSource { get; set; } // "title", "description", "comments"
    public double ConfidenceScore { get; set; } // 0-1, 地點識別的信心度
    public string GoogleMapsUrl => !string.IsNullOrEmpty(PlaceId) 
        ? $"https://www.google.com/maps/place/?q=place_id:{PlaceId}"
        : Latitude.HasValue && Longitude.HasValue 
            ? $"https://www.google.com/maps/@{Latitude},{Longitude},15z"
            : string.Empty;
}

public class YouTubeVideoStatistics
{
    public long? ViewCount { get; set; }
    public long? LikeCount { get; set; }
    public long? DislikeCount { get; set; }
    public long? CommentCount { get; set; }
    public long? FavoriteCount { get; set; }
    public double? AverageRating { get; set; }
    public DateTime? LastUpdated { get; set; }
    
    // 計算屬性
    public double LikeRatio => (LikeCount ?? 0) + (DislikeCount ?? 0) > 0 
        ? (double)(LikeCount ?? 0) / ((LikeCount ?? 0) + (DislikeCount ?? 0)) 
        : 0;
    
    public double EngagementRate => ViewCount > 0 
        ? (double)((LikeCount ?? 0) + (DislikeCount ?? 0) + (CommentCount ?? 0)) / ViewCount.Value 
        : 0;
}

public class YouTubeMetadataCollectionResult
{
    public bool Success { get; set; }
    public required YouTubeVideoInfo VideoInfo { get; set; }
    public YouTubeChannelInfo? ChannelInfo { get; set; }
    public List<YouTubeComment> Comments { get; set; } = [];
    public List<PlaceReference> DetectedPlaces { get; set; } = [];
    public List<string> ExtractedKeywords { get; set; } = [];
    public List<SubtitleLanguageInfo> AvailableSubtitles { get; set; } = [];
    public string? ErrorMessage { get; set; }
    public TimeSpan CollectionDuration { get; set; }
    public Dictionary<string, object> AdditionalMetadata { get; set; } = [];
}

public class GooglePlacesOptions
{
    public string ApiKey { get; set; } = string.Empty;
    public int SearchRadius { get; set; } = 5000;
    public string Language { get; set; } = "zh-TW";
    public string RegionCode { get; set; } = "TW";
    public bool IncludePriceLevel { get; set; } = true;
    public bool IncludeOpeningHours { get; set; } = true;
    public bool IncludeReviews { get; set; } = false;
    public List<string> PreferredTypes { get; set; } = ["restaurant", "tourist_attraction", "lodging", "point_of_interest", "establishment"];
    public int TimeoutSeconds { get; set; } = 30;
    public bool EnablePlaceDetection { get; set; } = false;
    public GooglePlacesDetectionOptions Detection { get; set; } = new();
}

public class GooglePlacesDetectionOptions
{
    public double ConfidenceThreshold { get; set; } = 0.6;
    public int MaxPlacesPerSource { get; set; } = 10;
    public int MaxTotalPlaces { get; set; } = 30;
}
