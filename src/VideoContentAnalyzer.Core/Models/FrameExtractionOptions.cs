namespace VideoContentAnalyzer.Core.Models;

public class FrameExtractionOptions
{
    public string OutputDirectory { get; set; } = "./data/frames";
    public bool UseTimestampInFilename { get; set; } = true;
    public string ImageFormat { get; set; } = "jpg";
    public int ImageQuality { get; set; } = 90;
    public bool KeepFrames { get; set; } = false;
    public bool CreateSubDirectoryPerVideo { get; set; } = true;
    public string SubDirectoryNameFormat { get; set; } = "{videoName}_{timestamp:yyyyMMdd_HHmmss}";
}

public class AdvancedOptions
{
    public int MaxConcurrentFrameAnalysis { get; set; } = 3;
    public bool EnableResultCache { get; set; } = true;
    public string CacheDirectory { get; set; } = "./data/cache";
    public int MaxRetryAttempts { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 2;
}