using System;
using System.IO;
using System.Reflection;

namespace VideoContentAnalyzer.Core.Utilities;

/// <summary>
/// 專案路徑輔助工具，用於解析相對於專案根目錄的路徑
/// </summary>
public static class ProjectPathHelper
{
    private static string? _projectRoot;
    
    /// <summary>
    /// 取得專案根目錄路徑
    /// </summary>
    public static string ProjectRoot
    {
        get
        {
            if (_projectRoot != null)
                return _projectRoot;
                
            _projectRoot = FindProjectRoot();
            return _projectRoot;
        }
    }
    
    /// <summary>
    /// 解析相對於專案根目錄的路徑
    /// </summary>
    /// <param name="relativePath">相對路徑</param>
    /// <returns>絕對路徑</returns>
    public static string ResolveProjectPath(string relativePath)
    {
        if (string.IsNullOrEmpty(relativePath))
            throw new ArgumentException("Path cannot be null or empty", nameof(relativePath));
            
        // 處理特殊路徑字符
        if (relativePath.StartsWith("~/"))
        {
            var homePath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            return Path.Combine(homePath, relativePath.Substring(2));
        }
        
        // 如果已經是絕對路徑，直接返回
        if (Path.IsPathRooted(relativePath))
        {
            return relativePath;
        }
        
        // 相對路徑相對於專案根目錄
        return Path.GetFullPath(Path.Combine(ProjectRoot, relativePath));
    }
    
    /// <summary>
    /// 尋找專案根目錄
    /// </summary>
    private static string FindProjectRoot()
    {
        // 從當前執行檔案的位置開始尋找
        var currentDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        
        if (currentDirectory == null)
        {
            // 如果無法取得執行檔案位置，使用當前工作目錄
            currentDirectory = Directory.GetCurrentDirectory();
        }
        
        var directory = new DirectoryInfo(currentDirectory);
        
        // 向上尋找包含 .sln 檔案的目錄
        while (directory != null)
        {
            // 檢查是否有 .sln 檔案
            if (directory.GetFiles("*.sln").Length > 0)
            {
                return directory.FullName;
            }
            
            // 檢查是否有 src 目錄（作為備用指標）
            if (directory.GetDirectories("src").Length > 0)
            {
                return directory.FullName;
            }
            
            directory = directory.Parent;
        }
        
        // 如果找不到，回退到當前工作目錄
        return Directory.GetCurrentDirectory();
    }
    
    /// <summary>
    /// 確保目錄存在
    /// </summary>
    /// <param name="path">目錄路徑</param>
    public static void EnsureDirectoryExists(string path)
    {
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
        }
    }
    
    /// <summary>
    /// 取得專案 data 目錄的路徑
    /// </summary>
    public static string DataDirectory => ResolveProjectPath("data");
    
    /// <summary>
    /// 取得專案 data/frames 目錄的路徑
    /// </summary>
    public static string FramesDirectory => ResolveProjectPath("data/frames");
    
    /// <summary>
    /// 取得專案 data/downloads 目錄的路徑
    /// </summary>
    public static string DownloadsDirectory => ResolveProjectPath("data/downloads");
    
    /// <summary>
    /// 取得專案 data/cache 目錄的路徑
    /// </summary>
    public static string CacheDirectory => ResolveProjectPath("data/cache");
    
    /// <summary>
    /// 取得專案 data/audio 目錄的路徑
    /// </summary>
    public static string AudioDirectory => ResolveProjectPath("data/audio");
}
