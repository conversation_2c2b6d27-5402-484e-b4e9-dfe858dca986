using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http;
using System.Text.Json;
using System.Text.RegularExpressions;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Infrastructure.Places;

public class GooglePlacesDetectionService : IPlaceDetectionService
{
    private readonly HttpClient _httpClient;
    private readonly GooglePlacesOptions _options;
    private readonly ILogger<GooglePlacesDetectionService> _logger;
    private static readonly Regex LocationRegex = new(
        @"(\p{L}+(?:\s+\p{L}+)*(?:\s*[\p{P}]\s*\p{L}+(?:\s+\p{L}+)*)*)", 
        RegexOptions.IgnoreCase | RegexOptions.Compiled);

    public GooglePlacesDetectionService(
        HttpClient httpClient, 
        IOptions<GooglePlacesOptions> options, 
        ILogger<GooglePlacesDetectionService> logger)
    {
        _httpClient = httpClient;
        _options = options.Value;
        _logger = logger;
    }

    public bool IsEnabled => !string.IsNullOrEmpty(_options.ApiKey) && 
                           _options.ApiKey != "YOUR_GOOGLE_MAPS_API_KEY_HERE";

    public async Task<List<PlaceReference>> DetectPlacesFromTextAsync(string text, string source = "unknown", CancellationToken cancellationToken = default)
    {
        if (!IsEnabled || string.IsNullOrWhiteSpace(text))
        {
            return new List<PlaceReference>();
        }

        var detectedPlaces = new List<PlaceReference>();

        try
        {
            _logger.LogDebug("正在從文字中檢測地點: {Source}, 文字長度: {Length}", source, text.Length);

            List<string> potentialPlaces;
            
            // 特殊處理：如果來源是 video_analysis（餐廳名稱確認），直接使用輸入文字
            if (source == "video_analysis")
            {
                potentialPlaces = new List<string> { text.Trim() };
                _logger.LogDebug("直接使用餐廳名稱進行搜尋: {Name}", text.Trim());
            }
            else
            {
                // 1. 使用正規表達式找出可能的地點名稱
                potentialPlaces = ExtractPotentialPlaceNames(text);
            }
            
            _logger.LogDebug("找到 {Count} 個潛在地點名稱", potentialPlaces.Count);

            // 2. 對每個潛在地點進行 Google Places 搜尋
            foreach (var placeName in potentialPlaces.Take(5)) // 限制搜尋數量避免過多 API 呼叫
            {
                var searchResults = await SearchPlacesByNameAsync(placeName, cancellationToken);
                
                foreach (var place in searchResults.Take(2)) // 每個名稱最多取 2 個結果
                {
                    place.DetectionSource = source;
                    place.ConfidenceScore = CalculateConfidenceScore(placeName, place, text);
                    
                    // 對於 video_analysis 來源，放寬信心度門檻
                    var confidenceThreshold = source == "video_analysis" ? 0.3 : _options.Detection.ConfidenceThreshold;
                    
                    // 只保留信心度較高的結果
                    if (place.ConfidenceScore >= confidenceThreshold)
                    {
                        detectedPlaces.Add(place);
                        _logger.LogDebug("找到匹配地點: {PlaceName} (信心度: {Score})", place.Name, place.ConfidenceScore);
                    }
                    else
                    {
                        _logger.LogDebug("地點信心度過低: {PlaceName} (信心度: {Score})", place.Name, place.ConfidenceScore);
                    }
                }

                // 避免過於頻繁的 API 呼叫
                await Task.Delay(200, cancellationToken);
            }

            // 3. 去重並排序
            var uniquePlaces = detectedPlaces
                .GroupBy(p => p.PlaceId ?? p.Name)
                .Select(g => g.OrderByDescending(p => p.ConfidenceScore).First())
                .OrderByDescending(p => p.ConfidenceScore)
                .ToList();

            _logger.LogInformation("從文字中檢測到 {Count} 個地點 (來源: {Source})", uniquePlaces.Count, source);

            return uniquePlaces;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "從文字檢測地點時發生錯誤: {Source}", source);
            return detectedPlaces;
        }
    }

    public async Task<List<PlaceReference>> DetectPlacesFromVideoInfoAsync(YouTubeVideoInfo videoInfo, CancellationToken cancellationToken = default)
    {
        var allPlaces = new List<PlaceReference>();

        try
        {
            _logger.LogInformation("正在從影片資訊中檢測地點: {VideoId}", videoInfo.Id);

            var tasks = new List<Task<List<PlaceReference>>>();

            // 從標題檢測
            if (!string.IsNullOrEmpty(videoInfo.Title))
            {
                tasks.Add(DetectPlacesFromTextAsync(videoInfo.Title, "title", cancellationToken));
            }

            // 從描述檢測
            if (!string.IsNullOrEmpty(videoInfo.Description))
            {
                tasks.Add(DetectPlacesFromTextAsync(videoInfo.Description, "description", cancellationToken));
            }

            // 從留言檢測 (取前 10 個最熱門的留言)
            if (videoInfo.TopComments?.Any() == true)
            {
                var topComments = string.Join(" ", videoInfo.TopComments
                    .OrderByDescending(c => c.LikeCount)
                    .Take(10)
                    .Select(c => c.Text));
                    
                if (!string.IsNullOrEmpty(topComments))
                {
                    tasks.Add(DetectPlacesFromTextAsync(topComments, "comments", cancellationToken));
                }
            }

            var results = await Task.WhenAll(tasks);
            
            foreach (var places in results)
            {
                allPlaces.AddRange(places);
            }

            // 合併和去重
            var uniquePlaces = allPlaces
                .GroupBy(p => p.PlaceId ?? p.Name)
                .Select(g => 
                {
                    var bestPlace = g.OrderByDescending(p => p.ConfidenceScore).First();
                    // 合併檢測來源資訊
                    bestPlace.DetectionSource = string.Join(", ", g.Select(p => p.DetectionSource).Distinct());
                    return bestPlace;
                })
                .Where(p => p.ConfidenceScore >= _options.Detection.ConfidenceThreshold)
                .OrderByDescending(p => p.ConfidenceScore)
                .Take(_options.Detection.MaxTotalPlaces)
                .ToList();

            _logger.LogInformation("從影片 {VideoId} 檢測到 {Count} 個地點", videoInfo.Id, uniquePlaces.Count);

            return uniquePlaces;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "從影片資訊檢測地點時發生錯誤: {VideoId}", videoInfo.Id);
            return allPlaces;
        }
    }

    public async Task<PlaceReference?> GetPlaceDetailsAsync(string placeId, CancellationToken cancellationToken = default)
    {
        if (!IsEnabled || string.IsNullOrEmpty(placeId))
            return null;

        try
        {
            var url = $"https://maps.googleapis.com/maps/api/place/details/json" +
                     $"?place_id={placeId}" +
                     $"&fields=name,formatted_address,geometry,rating,user_ratings_total,price_level,types,opening_hours,formatted_phone_number,website,reviews" +
                     $"&key={_options.ApiKey}";

            var response = await _httpClient.GetStringAsync(url);
            var jsonDoc = JsonDocument.Parse(response);
            var root = jsonDoc.RootElement;

            if (root.GetProperty("status").GetString() == "OK" && root.TryGetProperty("result", out var result))
            {
                return ParsePlaceFromJson(result);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "取得地點詳細資訊時發生錯誤: {PlaceId}", placeId);
            return null;
        }
    }

    public async Task<List<PlaceReference>> SearchNearbyPlacesAsync(double latitude, double longitude, int radiusMeters = 5000, CancellationToken cancellationToken = default)
    {
        if (!IsEnabled)
            return new List<PlaceReference>();

        try
        {
            var url = $"https://maps.googleapis.com/maps/api/place/nearbysearch/json" +
                     $"?location={latitude},{longitude}" +
                     $"&radius={radiusMeters}" +
                     $"&type=restaurant|tourist_attraction|lodging" +
                     $"&key={_options.ApiKey}";

            var response = await _httpClient.GetStringAsync(url);
            var jsonDoc = JsonDocument.Parse(response);
            var root = jsonDoc.RootElement;

            if (root.GetProperty("status").GetString() == "OK" && root.TryGetProperty("results", out var results))
            {
                var places = new List<PlaceReference>();
                
                foreach (var place in results.EnumerateArray().Take(20))
                {
                    var placeRef = ParsePlaceFromJson(place);
                    if (placeRef != null)
                    {
                        places.Add(placeRef);
                    }
                }

                return places;
            }

            return new List<PlaceReference>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "搜尋附近地點時發生錯誤: ({Lat}, {Lng})", latitude, longitude);
            return new List<PlaceReference>();
        }
    }

    private List<string> ExtractPotentialPlaceNames(string text)
    {
        var potentialPlaces = new List<string>();
        
        // 地點關鍵字模式
        var locationKeywords = new[]
        {
            // 中文
            "餐廳", "咖啡廳", "小吃", "夜市", "老街", "景點", "公園", "博物館", "廟宇", "車站", "機場",
            "飯店", "民宿", "商場", "百貨", "市場", "街", "路", "區", "市", "縣", "鎮", "鄉", "村",
            // 日文 (常見於旅遊影片)
            "レストラン", "カフェ", "駅", "空港", "ホテル", "神社", "寺", "公園", "美術館",
            // 英文
            "restaurant", "cafe", "hotel", "museum", "park", "station", "airport", "temple", "shrine"
        };

        var keywordPattern = string.Join("|", locationKeywords.Select(Regex.Escape));
        var locationKeywordRegex = new Regex($@"[\\p{{L}}\\s]+(?:{keywordPattern})[\\p{{L}}\\s]*", RegexOptions.IgnoreCase);

        // 1. 找出包含地點關鍵字的片段
        var matches = locationKeywordRegex.Matches(text);
        foreach (Match match in matches)
        {
            var candidate = match.Value.Trim();
            if (candidate.Length >= 3 && candidate.Length <= 50)
            {
                potentialPlaces.Add(candidate);
            }
        }

        // 2. 找出可能的專有名詞 (大寫開頭的詞彙組合)
        var properNounRegex = new Regex(@"\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b");
        matches = properNounRegex.Matches(text);
        foreach (Match match in matches)
        {
            var candidate = match.Value.Trim();
            if (candidate.Length >= 3 && candidate.Length <= 30)
            {
                potentialPlaces.Add(candidate);
            }
        }

        // 3. 中文地點名稱模式
        var chineseLocationRegex = new Regex(@"[\u4e00-\u9fff]+(?:市|縣|區|鎮|鄉|村|街|路|里|巷|弄)");
        matches = chineseLocationRegex.Matches(text);
        foreach (Match match in matches)
        {
            potentialPlaces.Add(match.Value.Trim());
        }

        return potentialPlaces.Distinct().Where(p => !string.IsNullOrWhiteSpace(p)).ToList();
    }

    private async Task<List<PlaceReference>> SearchPlacesByNameAsync(string placeName, CancellationToken cancellationToken)
    {
        try
        {
            var url = $"https://maps.googleapis.com/maps/api/place/textsearch/json" +
                     $"?query={Uri.EscapeDataString(placeName)}" +
                     $"&key={_options.ApiKey}";

            _logger.LogDebug("呼叫 Google Places API: {PlaceName}", placeName);
            
            var response = await _httpClient.GetStringAsync(url);
            var jsonDoc = JsonDocument.Parse(response);
            var root = jsonDoc.RootElement;

            var status = root.GetProperty("status").GetString();
            _logger.LogDebug("Google Places API 回應狀態: {Status} (查詢: {PlaceName})", status, placeName);

            if (status == "OK" && root.TryGetProperty("results", out var results))
            {
                var places = new List<PlaceReference>();
                var resultCount = results.GetArrayLength();
                
                _logger.LogInformation("Google Places API 找到 {Count} 個結果 (查詢: {PlaceName})", resultCount, placeName);
                
                foreach (var place in results.EnumerateArray().Take(3))
                {
                    var placeRef = ParsePlaceFromJson(place);
                    if (placeRef != null)
                    {
                        places.Add(placeRef);
                        _logger.LogInformation("解析到地點: {Name} | 地址: {Address} | 評分: {Rating} | PlaceID: {PlaceId}", 
                            placeRef.Name, 
                            placeRef.FormattedAddress ?? "無地址", 
                            placeRef.Rating?.ToString("F1") ?? "無評分", 
                            placeRef.PlaceId ?? "無PlaceID");
                    }
                }

                if (places.Count == 0)
                {
                    _logger.LogWarning("Google Places API 回傳結果但無法解析任何地點 (查詢: {PlaceName})", placeName);
                }

                return places;
            }
            else if (status == "ZERO_RESULTS")
            {
                _logger.LogInformation("Google Places API 沒有找到結果 (查詢: {PlaceName})", placeName);
            }
            else if (status == "REQUEST_DENIED")
            {
                _logger.LogError("Google Places API 請求被拒絕 - 請檢查 API 金鑰和配額 (查詢: {PlaceName})", placeName);
            }
            else if (status == "OVER_QUERY_LIMIT")
            {
                _logger.LogError("Google Places API 查詢配額已超過 (查詢: {PlaceName})", placeName);
            }
            else
            {
                _logger.LogWarning("Google Places API 回傳未知狀態: {Status} (查詢: {PlaceName})", status, placeName);
            }

            return new List<PlaceReference>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜尋地點時發生錯誤: {PlaceName}", placeName);
            return new List<PlaceReference>();
        }
    }

    private PlaceReference? ParsePlaceFromJson(JsonElement place)
    {
        try
        {
            var placeRef = new PlaceReference
            {
                Name = place.TryGetProperty("name", out var name) ? name.GetString() ?? "" : "",
                FormattedAddress = place.TryGetProperty("formatted_address", out var address) ? address.GetString() : null,
                PlaceId = place.TryGetProperty("place_id", out var placeId) ? placeId.GetString() : null,
                Rating = place.TryGetProperty("rating", out var rating) ? rating.GetDouble() : null,
                UserRatingsTotal = place.TryGetProperty("user_ratings_total", out var ratingsTotal) ? ratingsTotal.GetInt32() : null
            };

            // 解析座標
            if (place.TryGetProperty("geometry", out var geometry) && 
                geometry.TryGetProperty("location", out var location))
            {
                placeRef.Latitude = location.TryGetProperty("lat", out var lat) ? lat.GetDouble() : null;
                placeRef.Longitude = location.TryGetProperty("lng", out var lng) ? lng.GetDouble() : null;
            }

            // 解析類型
            if (place.TryGetProperty("types", out var types))
            {
                placeRef.Types = types.EnumerateArray()
                    .Select(t => t.GetString() ?? "")
                    .Where(t => !string.IsNullOrEmpty(t))
                    .ToList();
            }

            // 解析價格等級
            if (place.TryGetProperty("price_level", out var priceLevel))
            {
                placeRef.PriceLevel = priceLevel.GetInt32() switch
                {
                    0 => "免費",
                    1 => "便宜",
                    2 => "中等",
                    3 => "昂貴",
                    4 => "非常昂貴",
                    _ => null
                };
            }

            // 解析營業狀態
            if (place.TryGetProperty("opening_hours", out var openingHours))
            {
                placeRef.OpenNow = openingHours.TryGetProperty("open_now", out var openNow) ? openNow.GetBoolean() : null;
                
                if (openingHours.TryGetProperty("weekday_text", out var weekdayText))
                {
                    placeRef.OpeningHours = weekdayText.EnumerateArray()
                        .Select(w => w.GetString() ?? "")
                        .Where(w => !string.IsNullOrEmpty(w))
                        .ToList();
                }
            }

            return string.IsNullOrEmpty(placeRef.Name) ? null : placeRef;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "解析地點 JSON 時發生錯誤");
            return null;
        }
    }

    private double CalculateConfidenceScore(string searchTerm, PlaceReference place, string originalText)
    {
        double score = 0.0;

        // 基礎分數：地點名稱與搜尋詞的相似度
        if (!string.IsNullOrEmpty(place.Name))
        {
            var similarity = CalculateStringSimilarity(searchTerm.ToLower(), place.Name.ToLower());
            score += similarity * 0.4;
        }

        // 評分加分
        if (place.Rating.HasValue && place.Rating.Value >= 4.0)
        {
            score += 0.2;
        }

        // 評論數量加分
        if (place.UserRatingsTotal.HasValue && place.UserRatingsTotal.Value >= 10)
        {
            score += 0.1;
        }

        // 地點類型相關性
        var relevantTypes = new[] { "restaurant", "tourist_attraction", "lodging", "food", "establishment", "point_of_interest" };
        if (place.Types.Any(t => relevantTypes.Contains(t)))
        {
            score += 0.2;
        }

        // 在原文中出現頻率
        var occurrences = Regex.Matches(originalText, Regex.Escape(searchTerm), RegexOptions.IgnoreCase).Count;
        score += Math.Min(occurrences * 0.1, 0.3);

        return Math.Min(score, 1.0);
    }

    private static double CalculateStringSimilarity(string a, string b)
    {
        if (string.IsNullOrEmpty(a) || string.IsNullOrEmpty(b))
            return 0.0;

        // 簡單的相似度計算（基於共同字符）
        var aSet = new HashSet<char>(a.ToLower());
        var bSet = new HashSet<char>(b.ToLower());
        var intersection = aSet.Intersect(bSet).Count();
        var union = aSet.Union(bSet).Count();
        
        return union == 0 ? 0.0 : (double)intersection / union;
    }
}

