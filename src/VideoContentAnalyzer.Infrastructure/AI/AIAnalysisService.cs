using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Infrastructure.AI;

public class AIAnalysisService : IAIAnalysisService
{
    private readonly IOpenRouterClient _openRouterClient;
    private readonly ILMStudioClient _lmStudioClient;
    private readonly ILogger<AIAnalysisService> _logger;
    private readonly IYouTubeMetadataFormatter _metadataFormatter;

    public AIAnalysisService(
        ILMStudioClient lmStudioClient,
        IOpenRouterClient openRouterClient,
        ILogger<AIAnalysisService> logger,
        IYouTubeMetadataFormatter metadataFormatter)
    {
        _lmStudioClient = lmStudioClient;
        _openRouterClient = openRouterClient;
        _logger = logger;
        _metadataFormatter = metadataFormatter;
    }

    public async Task<SceneDescription> AnalyzeFrameAsync(string framePath, CancellationToken cancellationToken = default)
    {
        var prompt = """
            請仔細分析這張影片截圖，**專門尋找和提取餐廳名稱及地址資訊**。請用繁體中文回應。

            **最高優先級（必須詳細檢查）：**
            1. **餐廳/店鋪名稱**：
               - 招牌上的店名（中文、日文、韓文、英文）
               - Logo 旁的文字
               - 門口標示的店名
               - 任何看起來像店名的文字組合
               
            2. **飲食類型分類**：
               - **主食類**：烏龍麵、拉麵、蕎麥麵、義大利麵、蓋飯、丼物、咖喱、炒飯、便當
               - **料理風格**：和食、洋食、中華料理、韓式料理、泰式料理、義式料理、法式料理
               - **特殊類型**：燒肉、火鍋、壽司、刺身、天婦羅、居酒屋、串燒、鐵板燒
               - **輕食甜點**：甜點、蛋糕、麵包、咖啡廳、茶屋、冰淇淋、手搖飲
               - **快餐速食**：漢堡、炸雞、披薩、三明治、沙拉
               - 請根據招牌、菜單、裝潢風格、食物圖片等線索判斷料理類型
               
            3. **地址資訊**：
               - 完整街道地址（路名、門牌號碼）
               - 區域/街區名稱
               - 城市/區域標示
               - 任何看起來像地址的數字+街道組合
               - 郵政編碼

            4. **相關營業資訊**：
               - 電話號碼
               - 營業時間
               - 網址或社群媒體帳號

            **特別注意地址格式：**
            - 日式：〒郵遞區號、XX都/道/府/縣、XX市/區/町/村、XX丁目XX番XX號
            - 韓式：XX특별시/도、XX구/시、XX동/읍/면、XX番地/호
            - 中式：XX省/市、XX區/縣、XX路/街XX號
            - 台式：XX縣/市、XX區/鄉/鎮、XX路/街XX段XX號

            請嚴格按照以下 JSON 格式回應（每個欄位都必須是字串，不可使用巢狀物件）：
            {
                "mainDescription": "場景簡要描述，重點描述是否為餐廳場景及發現的名稱地址資訊",
                "cuisineType": "識別到的飲食類型，如：和食、烏龍麵、甜點、洋食等，如果不確定或非餐廳場景則填入'未知'",
                "restaurantCategory": "餐廳主要分類，如：麵食店、甜點店、咖啡廳、燒肉店、居酒屋等，非餐廳場景填入'非餐廳'",
                "visibleTexts": ["所有可見文字1", "文字2"],
                "activities": ["主要活動1", "活動2"],
                "objects": [{"name": "重要物件", "confidence": 0.9}],
                "setting": "地點和環境描述，如果發現地址資訊請在此詳細描述",
                "colors": ["主要顏色1", "顏色2"],
                "mood": "整體氛圍"
            }
            """;

        try
        {
            var (response, duration) = await _lmStudioClient.SendVisionRequestAsync(framePath, prompt, cancellationToken);
            var result = ParseSceneDescription(response);
            _logger.LogInformation("Frame analysis completed for {FramePath} in {Duration}ms", 
                Path.GetFileName(framePath), duration.TotalMilliseconds);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing frame: {FramePath}", framePath);
            return new SceneDescription
            {
                MainDescription = "Analysis failed"
            };
        }
    }

    public async Task<(SceneDescription result, TimeSpan duration)> AnalyzeFrameWithTimingAsync(string framePath, CancellationToken cancellationToken = default)
    {
        var prompt = """
            請仔細分析這張影片截圖，**專門尋找和提取餐廳名稱及地址資訊**。請用繁體中文回應。

            **最高優先級（必須詳細檢查）：**
            1. **餐廳/店鋪名稱**：
               - 招牌上的店名（中文、日文、韓文、英文）
               - Logo 旁的文字
               - 門口標示的店名
               - 任何看起來像店名的文字組合
               
            2. **飲食類型分類**：
               - **主食類**：烏龍麵、拉麵、蕎麥麵、義大利麵、蓋飯、丼物、咖喱、炒飯、便當
               - **料理風格**：和食、洋食、中華料理、韓式料理、泰式料理、義式料理、法式料理
               - **特殊類型**：燒肉、火鍋、壽司、刺身、天婦羅、居酒屋、串燒、鐵板燒
               - **輕食甜點**：甜點、蛋糕、麵包、咖啡廳、茶屋、冰淇淋、手搖飲
               - **快餐速食**：漢堡、炸雞、披薩、三明治、沙拉
               - 請根據招牌、菜單、裝潢風格、食物圖片等線索判斷料理類型
               
            3. **地址資訊**：
               - 完整街道地址（路名、門牌號碼）
               - 區域/街區名稱
               - 城市/區域標示
               - 任何看起來像地址的數字+街道組合
               - 郵政編碼

            4. **相關營業資訊**：
               - 電話號碼
               - 營業時間
               - 網址或社群媒體帳號

            **特別注意地址格式：**
            - 日式：〒郵遞區號、XX都/道/府/縣、XX市/區/町/村、XX丁目XX番XX號
            - 韓式：XX특별시/도、XX구/시、XX동/읍/면、XX番地/호
            - 中式：XX省/市、XX區/縣、XX路/街XX號
            - 台式：XX縣/市、XX區/鄉/鎮、XX路/街XX段XX號

            請嚴格按照以下 JSON 格式回應（每個欄位都必須是字串，不可使用巢狀物件）：
            {
                "mainDescription": "場景簡要描述，重點描述是否為餐廳場景及發現的名稱地址資訊",
                "cuisineType": "識別到的飲食類型，如：和食、烏龍麵、甜點、洋食等，如果不確定或非餐廳場景則填入'未知'",
                "restaurantCategory": "餐廳主要分類，如：麵食店、甜點店、咖啡廳、燒肉店、居酒屋等，非餐廳場景填入'非餐廳'",
                "visibleTexts": ["所有可見文字1", "文字2"],
                "activities": ["主要活動1", "活動2"],
                "objects": [{"name": "重要物件", "confidence": 0.9}],
                "setting": "地點和環境描述，如果發現地址資訊請在此詳細描述",
                "colors": ["主要顏色1", "顏色2"],
                "mood": "整體氛圍"
            }
            """;

        try
        {
            var (response, duration) = await _lmStudioClient.SendVisionRequestAsync(framePath, prompt, cancellationToken);
            var result = ParseSceneDescription(response);
            _logger.LogInformation("Frame analysis with timing completed for {FramePath} in {Duration}ms", 
                Path.GetFileName(framePath), duration.TotalMilliseconds);
            return (result, duration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing frame: {FramePath}", framePath);
            return (new SceneDescription { MainDescription = "Analysis failed" }, TimeSpan.Zero);
        }
    }

    public async Task<(SceneDescription result, TimeSpan duration)> AnalyzeFrameWithTimingAsync(string framePath, YouTubeVideoInfo? youtubeMetadata, CancellationToken cancellationToken = default)
    {
        // 如果沒有 YouTube 元數據，使用原始方法
        if (youtubeMetadata == null)
        {
            return await AnalyzeFrameWithTimingAsync(framePath, cancellationToken);
        }

        // 格式化 YouTube 元數據為上下文
        var metadataContext = _metadataFormatter.FormatMetadataForAI(youtubeMetadata);

        var prompt = $$"""
            {{metadataContext}}

            請仔細分析這張影片截圖，**專門尋找和提取餐廳名稱及地址資訊**。請用繁體中文回應。

            **根據上述影片背景資訊，請特別注意：**
            - 如果影片標題或描述中提到特定地區，請優先考慮該地區的餐廳和地點
            - 如果影片標籤包含特定料理類型，請在分析時重點關注相關的餐廳類型
            - 如果檢測到特定地點資訊，請結合這些資訊來增強地址識別的準確性
            - 考慮頻道的地理位置和語言環境來輔助判斷

            **最高優先級（必須詳細檢查）：**
            1. **餐廳/店鋪名稱**：
               - 招牌上的店名（中文、日文、韓文、英文）
               - Logo 旁的文字
               - 門口標示的店名
               - 任何看起來像店名的文字組合

            2. **飲食類型分類**：
               - **主食類**：烏龍麵、拉麵、蕎麥麵、義大利麵、蓋飯、丼物、咖喱、炒飯、便當
               - **料理風格**：和食、洋食、中華料理、韓式料理、泰式料理、義式料理、法式料理
               - **特殊類型**：燒肉、火鍋、壽司、刺身、天婦羅、居酒屋、串燒、鐵板燒
               - **輕食甜點**：甜點、蛋糕、麵包、咖啡廳、茶屋、冰淇淋、手搖飲
               - **快餐速食**：漢堡、炸雞、披薩、三明治、沙拉
               - 請根據招牌、菜單、裝潢風格、食物圖片等線索判斷料理類型

            3. **地址資訊**：
               - 完整街道地址（路名、門牌號碼）
               - 區域/街區名稱
               - 城市/區域標示
               - 任何看起來像地址的數字+街道組合
               - 郵政編碼

            4. **相關營業資訊**：
               - 電話號碼
               - 營業時間
               - 網址或社群媒體帳號

            **特別注意地址格式：**
            - 日式：〒郵遞區號、XX都/道/府/縣、XX市/區/町/村、XX丁目XX番XX號
            - 韓式：XX특별시/도、XX구/시、XX동/읍/면、XX番地/호
            - 中式：XX省/市、XX區/縣、XX路/街XX號
            - 台式：XX縣/市、XX區/鄉/鎮、XX路/街XX段XX號

            請嚴格按照以下 JSON 格式回應（每個欄位都必須是字串，不可使用巢狀物件）：
            {
                "mainDescription": "場景簡要描述，重點描述是否為餐廳場景及發現的名稱地址資訊，並結合影片背景資訊提供更準確的分析",
                "cuisineType": "識別到的飲食類型，如：和食、烏龍麵、甜點、洋食等，如果不確定或非餐廳場景則填入'未知'",
                "restaurantCategory": "餐廳主要分類，如：麵食店、甜點店、咖啡廳、燒肉店、居酒屋等，非餐廳場景填入'非餐廳'",
                "visibleTexts": ["所有可見文字1", "文字2"],
                "activities": ["主要活動1", "活動2"],
                "objects": [{"name": "重要物件", "confidence": 0.9}],
                "setting": "地點和環境描述，如果發現地址資訊請在此詳細描述，並結合影片背景資訊",
                "colors": ["主要顏色1", "顏色2"],
                "mood": "整體氛圍"
            }
            """;

        try
        {
            var (response, duration) = await _lmStudioClient.SendVisionRequestAsync(framePath, prompt, cancellationToken);
            var result = ParseSceneDescription(response);
            _logger.LogInformation("Frame analysis with YouTube metadata completed for {FramePath} in {Duration}ms",
                Path.GetFileName(framePath), duration.TotalMilliseconds);
            return (result, duration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing frame with YouTube metadata: {FramePath}", framePath);
            return (new SceneDescription { MainDescription = "Analysis failed" }, TimeSpan.Zero);
        }
    }

    public async Task<List<PlaceInfo>> ExtractPlaceInfoAsync(string framePath, CancellationToken cancellationToken = default)
    {
        var prompt = """
            請專門分析圖片，**重點提取餐廳名稱和地址資訊**，並組織成結構化資料。請用繁體中文分析。

            **主要任務：餐廳名稱識別**
            仔細尋找以下可能的餐廳名稱來源：
            - 招牌主要文字（通常最大最醒目）
            - 店門標示
            - Logo 或品牌標誌旁的文字
            - 包裝、制服上的店名
            - 任何重複出現的商號文字

            **主要任務：地址資訊提取**
            搜尋各種地址格式和組成元素：

            **完整地址模式：**
            - 日本：〒100-0001 東京都千代田區千代田1-1
            - 韓國：서울특별시 강남구 테헤란로 123
            - 台灣：台北市信義區信義路四段101號
            - 香港：香港中環德輔道中123號
            - 中國：北京市朝陽區建國門外大街1號

            **地址組成要素：**
            1. **門牌號碼**：數字+路/街/道名稱組合
            2. **街道名稱**：路、街、大道、巷、弄、lane、street、road
            3. **區域名稱**：區、市、縣、州、district、ward
            4. **郵政編碼**：3-7位數字組合
            5. **地標建築**：大樓名、商場名、車站名

            **輔助資訊：**
            - 電話號碼（含國際格式：+81、+82、+86、+852、+886）
            - 網址和社群媒體帳號
            - 營業時間（各種格式：24小時制、12小時制）
            - 價格範圍或特殊服務

            **分析策略：**
            1. 先識別最醒目的文字作為潛在店名
            2. 尋找數字+文字組合作為可能地址
            3. 檢查是否有完整的地址格式
            4. 驗證電話號碼格式
            5. 組合相關資訊形成完整的場所資料

            請以 JSON 格式回應：
            {
                "placeInfos": [
                    {
                        "name": "餐廳名稱（如果識別到）",
                        "address": "完整地址（儘可能詳細）",
                        "phone": "電話號碼（包含區域碼）",
                        "website": "網址或社群帳號",
                        "businessHours": "營業時間",
                        "category": "餐廳類型（日式/韓式/中式/西式等）",
                        "description": "簡要描述或特色",
                        "confidence": 0.9,
                        "originalTexts": ["原始文字1", "原始文字2"]
                    }
                ]
            }

            **注意事項：**
            - 如果只能識別部分資訊，請仍然回報（如只有店名沒有地址）
            - 標註信心度，不確定的資訊請降低信心度
            - 保留原始文字以供驗證
            - 如果沒有發現餐廳相關資訊，回傳空陣列
            """;

        try
        {
            var (response, _) = await _lmStudioClient.SendVisionRequestAsync(framePath, prompt, cancellationToken);
            return ParsePlaceInfos(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting place info from frame: {FramePath}", framePath);
            return [];
        }
    }

    public async Task<List<DetectedText>> ExtractTextFromFrameAsync(string framePath, CancellationToken cancellationToken = default)
    {
        var prompt = """
            請專門提取圖片中可能為**餐廳名稱**或**地址資訊**的所有文字。請用繁體中文分析。

            **重點搜尋目標（按重要性排序）：**
            
            **1. 餐廳/店鋪名稱相關文字：**
            - 招牌上的主要店名
            - 副標題或品牌名稱
            - Logo 旁的文字
            - "XX餐廳"、"XX食堂"、"XX亭"、"XX屋"等
            - 任何看起來像商號的文字組合

            **2. 地址相關文字：**
            - 門牌號碼（數字+路名組合）
            - 街道名稱（路、街、巷、弄等）
            - 區域名稱（區、市、縣、州等）
            - 郵政編碼
            - 地標建築物名稱
            - 任何包含地址元素的文字串

            **3. 聯絡資訊文字：**
            - 電話號碼（含區域碼）
            - 網址（www、.com、.jp、.kr等）
            - 營業時間相關文字

            **4. 其他可見文字：**
            - 菜單項目和價格
            - 字幕文字
            - 廣告文字

            **特別檢查區域：**
            - 招牌頂部和底部
            - 門口周圍
            - 窗戶貼紙和告示
            - 菜單板
            - 建築物外牆標示

            **文字識別要求：**
            - 保持原文不翻譯
            - 包含垂直排列的文字
            - 注意模糊或部分遮擋的文字
            - 識別各種字體和大小

            請以 JSON 格式回應：
            {
                "detectedTexts": [
                    {
                        "text": "實際文字內容",
                        "confidence": 0.95,
                        "language": "zh-TW",
                        "location": "招牌/地址牌/菜單/字幕"
                    }
                ]
            }
            """;

        try
        {
            var (response, _) = await _lmStudioClient.SendVisionRequestAsync(framePath, prompt, cancellationToken);
            return ParseDetectedTexts(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting text from frame: {FramePath}", framePath);
            return [];
        }
    }

    public async Task<List<SceneDescription>> AnalyzeFramesBatchAsync(IEnumerable<string> framePaths, CancellationToken cancellationToken = default)
    {
        var tasks = framePaths.Select(async path => await AnalyzeFrameAsync(path, cancellationToken));
        return (await Task.WhenAll(tasks)).ToList();
    }

    public async Task<SubtitleAnalysis> AnalyzeSubtitleSegmentAsync(string text, CancellationToken cancellationToken = default)
    {
        var prompt = $@"分析以下字幕文字的語義內容（支援中文、日文、韓文等多語言）:
""{text}""

請提供以下分析，並使用繁體中文回應:
1. 情感傾向（正面/負面/中性）
2. 主要話題或主題
3. 重要關鍵詞
4. 是對話還是旁白
5. 如果文字較長請提供簡要摘要

請以 JSON 格式回應:
{{
    ""sentiment"": ""中性"",
    ""topics"": [""話題1"", ""話題2""],
    ""keywords"": [""關鍵詞1"", ""關鍵詞2""],
    ""isDialog"": true,
    ""summary"": ""簡要摘要""
}}";

        try
        {
            var response = await _lmStudioClient.SendTextRequestAsync(prompt, cancellationToken);
            return ParseSubtitleAnalysis(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing subtitle segment");
            return new SubtitleAnalysis();
        }
    }

    public async Task<(string summaryText, string subtitleContent, List<string> subtitleSegments)> GenerateVideoSummaryAsync(List<FrameAnalysis> frameAnalyses, List<SubtitleSegment> subtitles, CancellationToken cancellationToken = default)
    {
        // 收集所有的餐廳/景點資訊（從 PlaceInfos）
        var allPlaceInfos = frameAnalyses
            .SelectMany(f => f.PlaceInfos)
            .Where(p => p.Confidence > 0.3 && !string.IsNullOrEmpty(p.Name)) // 過濾低信心度的結果和空名稱
            .GroupBy(p => p.Name)
            .Select(g => g.OrderByDescending(p => p.Confidence).First()) // 取信心度最高的
            .OrderByDescending(p => p.Confidence)
            .Take(10)
            .ToList();

        // 額外從 VisibleTexts 中收集可能的餐廳名稱（作為備份方案）
        var additionalRestaurantNames = frameAnalyses
            .Where(f => !string.IsNullOrEmpty(f.Scene.RestaurantCategory) && 
                       f.Scene.RestaurantCategory != "非餐廳" &&
                       f.Scene.VisibleTexts.Any())
            .SelectMany(f => f.Scene.VisibleTexts
                .Where(text => !string.IsNullOrWhiteSpace(text) && 
                              text.Length > 1 && 
                              text.Length < 50 &&
                              IsLikelyRestaurantName(text, f.Scene.CuisineType))
                .Select(text => new { Text = text, Scene = f.Scene }))
            .GroupBy(x => x.Text)
            .Select(g => g.First())
            .Take(5)
            .ToList();

        // 將額外發現的餐廳名稱轉換為 PlaceInfo 格式，避免重複
        var additionalPlaceInfos = additionalRestaurantNames
            .Where(item => !allPlaceInfos.Any(p => 
                string.Equals(p.Name, item.Text, StringComparison.OrdinalIgnoreCase)))
            .Select(item => new PlaceInfo
            {
                Name = item.Text,
                Category = item.Scene.RestaurantCategory != "非餐廳" ? item.Scene.RestaurantCategory : item.Scene.CuisineType,
                Description = item.Scene.MainDescription,
                Confidence = 0.6,
                OriginalTexts = [item.Text],
                Address = null,
                Phone = null,
                BusinessHours = null,
                Website = null
            })
            .ToList();

        // 合併所有餐廳資訊
        var combinedPlaceInfos = allPlaceInfos.Concat(additionalPlaceInfos)
            .OrderByDescending(p => p.Confidence)
            .Take(10)
            .ToList();

        // 分類地點資訊：有 Google Places 資料 vs 只有影片檢測資料
        var googlePlacesEnrichedPlaces = combinedPlaceInfos
            .Where(p => !string.IsNullOrEmpty(p.Address) || !string.IsNullOrEmpty(p.Phone) || !string.IsNullOrEmpty(p.Website))
            .ToList();
            
        var videoDetectedOnlyPlaces = combinedPlaceInfos
            .Where(p => string.IsNullOrEmpty(p.Address) && string.IsNullOrEmpty(p.Phone) && string.IsNullOrEmpty(p.Website))
            .ToList();

        // 收集所有可見文字
        var allVisibleTexts = frameAnalyses
            .SelectMany(f => f.Scene.VisibleTexts)
            .Where(t => !string.IsNullOrWhiteSpace(t))
            .Distinct()
            .Take(20)
            .ToList();

        var frameDescriptions = frameAnalyses
            .Take(10) // Limit to avoid token limits
            .Select(f => $"[{f.Timestamp:mm\\:ss}] {f.Scene.MainDescription}");
        
        var subtitleTexts = subtitles
            .Take(15) // 稍微減少字幕數量為地點資訊讓出空間
            .Select(s => $"[{s.StartTime:mm\\:ss}] {s.Text}")
            .ToList();

        // 建立更詳細的地點資訊區塊
        string placeInfoSection = "";
        
        if (googlePlacesEnrichedPlaces.Any())
        {
            placeInfoSection += $@"

=== 透過 Google Places API 驗證的餐廳/景點 ===
{string.Join("\n", googlePlacesEnrichedPlaces.Select(p => 
{
    var info = $"🏪 {p.Name ?? "未知名稱"}";
    if (!string.IsNullOrEmpty(p.Address))
        info += $"\n   📍 地址: {p.Address}";
    if (!string.IsNullOrEmpty(p.Phone))
        info += $"\n   📞 電話: {p.Phone}";
    if (!string.IsNullOrEmpty(p.Website))
        info += $"\n   🌐 網站: {p.Website}";
    if (!string.IsNullOrEmpty(p.Category))
        info += $"\n   🏷️ 類型: {p.Category}";
    if (!string.IsNullOrEmpty(p.BusinessHours))
        info += $"\n   🕒 營業時間: {p.BusinessHours}";
    if (p.GoogleRating.HasValue)
        info += $"\n   ⭐ 評分: {p.GoogleRating:F1}";
    info += $"\n   🎯 信心度: {p.Confidence:F1}";
    return info;
}))}";
        }
        
        if (videoDetectedOnlyPlaces.Any())
        {
            placeInfoSection += $@"

=== 影片中識別的其他餐廳/景點 ===
{string.Join("\n", videoDetectedOnlyPlaces.Select(p => 
    $"• {p.Name ?? "未知名稱"}" +
    (string.IsNullOrEmpty(p.Category) ? "" : $" ({p.Category})") +
    $" - 信心度: {p.Confidence:F1}"
))}";
        }

        var visibleTextSection = allVisibleTexts.Any() ? 
            $@"

影片中出現的重要文字:
{string.Join(", ", allVisibleTexts)}" : "";

        var prompt = $@"根據以下畫面分析和字幕內容創建這段影片的全面摘要，特別關注餐廳和景點資訊（支援中文、日文、韓文等多語言內容）:

畫面分析:
{string.Join("\n", frameDescriptions)}

字幕內容:
{string.Join("\n", subtitleTexts)}{placeInfoSection}{visibleTextSection}

請提供包含以下內容的摘要，使用繁體中文回應:

1. **已驗證的餐廳/景點資訊** (如果有透過 Google Places API 驗證的資訊):
   - 完整的餐廳名稱、確切地址、聯絡電話
   - 營業時間、網站連結、評分等官方資訊
   - 餐廳類型和特色

2. **影片中發現的其他地點** (影片中識別但未經 Google Places 驗證):
   - 可能的餐廳名稱和相關資訊
   - 基於影片內容的推測資訊

3. **整體主題和內容**:
   - 影片的主要主題和目的
   - 拍攝地點和文化背景

4. **觀察到的主要活動** (特別是與用餐、觀光相關的):
   - 用餐體驗、菜品介紹
   - 觀光活動、景點參觀

5. **影片中的重要事件或亮點**:
   - 特別值得注意的場景或時刻
   - 推薦的美食或體驗

6. **實用旅遊資訊** (基於識別到的資訊):
   - 如何前往、交通建議
   - 推薦理由和注意事項

格式要求:
- 如果有 Google Places 驗證的餐廳，請在摘要開頭重點列出，包含完整的聯絡資訊
- 區分「已驗證資訊」和「影片推測資訊」
- 提供實用的旅遊建議和參考資訊
- 摘要應該簡潔但資訊豐富（4-5段落）
- 如果有多語言內容，請適當說明語言環境";

        try
        {
            var summaryText = await _lmStudioClient.SendTextRequestAsync(prompt, cancellationToken);
            var subtitleContent = string.Join("\n", subtitleTexts);
            
            return (summaryText, subtitleContent, subtitleTexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating video summary");
            return ("Unable to generate video summary due to analysis error.", "", new List<string>());
        }
    }

    public async Task<(string summaryText, string subtitleContent, List<string> subtitleSegments)> GenerateVideoSummaryAsync(List<FrameAnalysis> frameAnalyses, List<SubtitleSegment> subtitles, YouTubeVideoInfo? youtubeMetadata, CancellationToken cancellationToken = default)
    {
        // 如果沒有 YouTube 元數據，使用原始方法
        if (youtubeMetadata == null)
        {
            return await GenerateVideoSummaryAsync(frameAnalyses, subtitles, cancellationToken);
        }

        // 格式化 YouTube 元數據為上下文
        var metadataContext = _metadataFormatter.FormatMetadataForAI(youtubeMetadata);

        // 收集所有的餐廳/景點資訊（從 PlaceInfos）
        var allPlaceInfos = frameAnalyses
            .SelectMany(f => f.PlaceInfos)
            .Where(p => p.Confidence > 0.3 && !string.IsNullOrEmpty(p.Name)) // 過濾低信心度的結果和空名稱
            .GroupBy(p => p.Name)
            .Select(g => g.OrderByDescending(p => p.Confidence).First()) // 取信心度最高的
            .OrderByDescending(p => p.Confidence)
            .Take(10)
            .ToList();

        // 額外從 VisibleTexts 中收集可能的餐廳名稱（作為備份方案）
        var additionalRestaurantNames = frameAnalyses
            .Where(f => !string.IsNullOrEmpty(f.Scene.RestaurantCategory) &&
                       f.Scene.RestaurantCategory != "非餐廳" &&
                       f.Scene.RestaurantCategory != "未知")
            .SelectMany(f => f.Scene.VisibleTexts)
            .Where(text => IsLikelyRestaurantName(text, null))
            .Distinct()
            .Take(5)
            .ToList();

        // 合併 PlaceInfos 和額外發現的餐廳名稱
        var combinedPlaceInfos = allPlaceInfos.ToList();
        foreach (var name in additionalRestaurantNames)
        {
            if (!combinedPlaceInfos.Any(p => !string.IsNullOrEmpty(p.Name) && (p.Name.Contains(name) || name.Contains(p.Name))))
            {
                combinedPlaceInfos.Add(new PlaceInfo
                {
                    Name = name,
                    Confidence = 0.6,
                    Category = "餐廳"
                });
            }
        }

        // 收集所有可見文字
        var allVisibleTexts = frameAnalyses
            .SelectMany(f => f.Scene.VisibleTexts)
            .Where(text => !string.IsNullOrWhiteSpace(text) && text.Length > 1)
            .Distinct()
            .ToList();

        // 收集字幕文字
        var subtitleTexts = subtitles
            .Where(s => !string.IsNullOrWhiteSpace(s.Text))
            .Select(s => s.Text.Trim())
            .ToList();

        var placeInfoSection = combinedPlaceInfos.Any() ?
            $@"

識別到的餐廳/景點資訊:
{string.Join("\n", combinedPlaceInfos.Select(p =>
    $"• {p.Name ?? "未知名稱"}" +
    (string.IsNullOrEmpty(p.Address) ? "" : $" - 地址: {p.Address}") +
    (string.IsNullOrEmpty(p.Phone) ? "" : $" - 電話: {p.Phone}") +
    (string.IsNullOrEmpty(p.Category) ? "" : $" - 類型: {p.Category}") +
    (string.IsNullOrEmpty(p.BusinessHours) ? "" : $" - 營業時間: {p.BusinessHours}")
))}" : "";

        var visibleTextSection = allVisibleTexts.Any() ?
            $@"

影片中出現的重要文字:
{string.Join(", ", allVisibleTexts)}" : "";

        var prompt = $@"{metadataContext}

根據以上影片背景資訊和以下畫面分析、字幕內容創建這段影片的全面摘要，特別關注餐廳和景點資訊（支援中文、日文、韓文等多語言內容）:

畫面分析結果:
{string.Join("\n", frameAnalyses.Select(f => $"時間 {f.Timestamp}: {f.Scene.MainDescription}"))}
{placeInfoSection}
{visibleTextSection}

字幕內容:
{string.Join("\n", subtitleTexts)}

請結合影片背景資訊來增強摘要的準確性和相關性，特別注意：
- 利用影片標題和描述來理解影片主題
- 參考檢測到的地點資訊來確認地理位置
- 結合頻道資訊和標籤來判斷內容類型
- 考慮影片的語言環境和文化背景

格式要求:
- 如果識別到餐廳或景點，請在摘要開頭以清單形式列出
- 每個地點包含: 名稱、地址（如果有）、類型、其他相關資訊
- 摘要應該簡潔但資訊豐富（3-4段落）
- 如果有多語言內容，請適當說明語言環境
- 結合影片背景資訊提供更深入的分析";

        try
        {
            var summaryText = await _lmStudioClient.SendTextRequestAsync(prompt, cancellationToken);
            var subtitleContent = string.Join("\n", subtitleTexts);

            return (summaryText, subtitleContent, subtitleTexts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating video summary with YouTube metadata");
            return ("Unable to generate video summary due to analysis error.", "", new List<string>());
        }
    }

    /// <summary>
    /// 判斷文字是否可能是餐廳名稱（增強版本，考慮料理類型）
    /// </summary>
    private static bool IsLikelyRestaurantName(string text, string? cuisineType)
    {
        // 排除純數字、單字元、常見非名稱文字
        if (string.IsNullOrWhiteSpace(text) ||
            text.All(char.IsDigit) || 
            text.Length <= 1 ||
            text.All(c => char.IsWhiteSpace(c) || char.IsPunctuation(c)))
            return false;

        // 排除明顯的非名稱文字
        var excludePatterns = new[] { "第", "位", "名", "営業中", "閉店", "open", "close", "menu", "price", "￥", "$", "円" };
        if (excludePatterns.Any(pattern => text.Contains(pattern, StringComparison.OrdinalIgnoreCase)))
            return false;

        // 包含餐廳相關關鍵字的更可能是店名
        var restaurantKeywords = new[] { "麺", "麵", "食堂", "店", "屋", "亭", "館", "家", "房", "kitchen", "cafe", "restaurant", "grill", "bar", "dining" };
        if (restaurantKeywords.Any(keyword => text.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
            return true;

        // 根據料理類型調整判斷
        if (!string.IsNullOrEmpty(cuisineType))
        {
            if (cuisineType.Contains("拉麵") || cuisineType.Contains("和食"))
            {
                var japaneseKeywords = new[] { "の", "や", "亭", "屋", "庵" };
                if (japaneseKeywords.Any(keyword => text.Contains(keyword)))
                    return true;
            }
            else if (cuisineType.Contains("韓式"))
            {
                // 韓文字符範圍
                if (text.Any(c => c >= 0xAC00 && c <= 0xD7AF))
                    return true;
            }
        }

        // 其他可能包含店名的模式（如日文、中文、韓文店名）
        if (text.Any(c => char.GetUnicodeCategory(c) == System.Globalization.UnicodeCategory.OtherLetter))
            return true;

        // 如果包含英文但不是常見的非名稱詞彙，也可能是店名
        if (text.Any(char.IsLetter) && text.Length >= 3)
        {
            var commonNonNames = new[] { "and", "the", "for", "with", "from", "menu", "open", "close", "time", "price" };
            if (!commonNonNames.Contains(text.ToLower()))
                return true;
        }

        return false;
    }

    private SceneDescription ParseSceneDescription(string response)
    {
        try
        {
            _logger.LogDebug("Parsing scene description from response: {Response}", response);
            
            // 清理回應，移除可能的換行符號和多餘空白
            var cleanedResponse = response?.Replace("\\n", "\n")?.Trim() ?? "";
            
            // 移除可能的 BOM 或其他無效字符
            cleanedResponse = RemoveInvalidCharacters(cleanedResponse);
            
            var jsonStart = cleanedResponse.IndexOf('{');
            var jsonEnd = cleanedResponse.LastIndexOf('}');
            
            _logger.LogDebug("JSON boundaries - Start: {Start}, End: {End}", jsonStart, jsonEnd);
            
            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                var jsonString = cleanedResponse.Substring(jsonStart, jsonEnd - jsonStart + 1);
                _logger.LogDebug("Extracted JSON string length: {Length}", jsonString.Length);
                
                // 更安全的 JSON 清理方式
                jsonString = CleanJsonString(jsonString);
                
                // 使用更寬鬆的 JSON 選項
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    AllowTrailingCommas = true,
                    ReadCommentHandling = JsonCommentHandling.Skip,
                    Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                // 嘗試多種解析方法
                JsonElement parsed;
                try
                {
                    parsed = JsonSerializer.Deserialize<JsonElement>(jsonString, options);
                }
                catch (JsonException)
                {
                    // 如果失敗，嘗試手動修復常見問題
                    jsonString = RepairCommonJsonIssues(jsonString);
                    parsed = JsonSerializer.Deserialize<JsonElement>(jsonString, options);
                }
                
                return new SceneDescription
                {
                    MainDescription = parsed.TryGetProperty("mainDescription", out var desc) ? desc.GetString() ?? "" : "",
                    CuisineType = parsed.TryGetProperty("cuisineType", out var cuisineType) ? cuisineType.GetString() ?? "" : "",
                    RestaurantCategory = parsed.TryGetProperty("restaurantCategory", out var restaurantCategory) ? restaurantCategory.GetString() ?? "" : "",
                    VisibleTexts = parsed.TryGetProperty("visibleTexts", out var visibleTexts) ? 
                        visibleTexts.EnumerateArray().Select(t => t.GetString() ?? "").Where(s => !string.IsNullOrWhiteSpace(s)).ToList() : [],
                    Activities = parsed.TryGetProperty("activities", out var activities) ? 
                        activities.EnumerateArray().Select(a => a.GetString() ?? "").Where(s => !string.IsNullOrWhiteSpace(s)).ToList() : [],
                    Setting = parsed.TryGetProperty("setting", out var setting) ? setting.GetString() ?? "" : "",
                    Colors = parsed.TryGetProperty("colors", out var colors) ? 
                        colors.EnumerateArray().Select(c => c.GetString() ?? "").Where(s => !string.IsNullOrWhiteSpace(s)).ToList() : [],
                    Mood = parsed.TryGetProperty("mood", out var mood) ? mood.GetString() ?? "" : ""
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse scene description JSON, using fallback. Response length: {Length}", response?.Length ?? 0);
            
            // 記錄前 200 個字符以便調試
            if (!string.IsNullOrEmpty(response) && response.Length > 10)
            {
                var preview = response.Length > 200 ? response.Substring(0, 200) + "..." : response;
                _logger.LogWarning("Response preview: {Preview}", preview);
            }
        }

        // 如果 JSON 解析失敗，嘗試從原始響應中提取有用信息
        return ExtractFallbackSceneDescription(response);
    }

    private static string CleanJsonString(string jsonString)
    {
        if (string.IsNullOrEmpty(jsonString))
            return jsonString;

        // 首先移除可能的 Markdown 代碼塊標記
        jsonString = jsonString.Replace("```json", "").Replace("```", "");

        // 處理字符串中的特殊字符
        var result = new StringBuilder();
        var inString = false;
        var escaping = false;

        for (int i = 0; i < jsonString.Length; i++)
        {
            var ch = jsonString[i];

            if (escaping)
            {
                // 處理轉義字符
                result.Append(ch);
                escaping = false;
                continue;
            }

            if (ch == '\\')
            {
                // 檢查是否是有效的轉義序列
                if (i + 1 < jsonString.Length)
                {
                    var nextChar = jsonString[i + 1];
                    if (inString && (nextChar == '"' || nextChar == '\\' || nextChar == '/' || 
                                   nextChar == 'b' || nextChar == 'f' || nextChar == 'n' || 
                                   nextChar == 'r' || nextChar == 't' || nextChar == 'u'))
                    {
                        // 有效的轉義序列，保持原樣
                        result.Append(ch);
                        escaping = true;
                    }
                    else if (inString)
                    {
                        // 在字符串中的無效反斜線，轉義它
                        result.Append("\\\\");
                    }
                    else
                    {
                        // 在字符串外的反斜線，可能是錯誤，跳過
                        continue;
                    }
                }
                else if (inString)
                {
                    // 字符串末尾的反斜線，轉義它
                    result.Append("\\\\");
                }
                continue;
            }

            if (ch == '"' && !escaping)
            {
                inString = !inString;
            }

            // 處理字符串中的實際換行符
            if (inString && (ch == '\n' || ch == '\r'))
            {
                if (ch == '\r' && i + 1 < jsonString.Length && jsonString[i + 1] == '\n')
                {
                    result.Append("\\n");
                    i++; // 跳過 \n
                }
                else if (ch == '\n')
                {
                    result.Append("\\n");
                }
                else
                {
                    result.Append("\\n");
                }
                continue;
            }

            result.Append(ch);
        }

        return result.ToString().Trim();
    }

    /// <summary>
    /// 移除字符串中的無效字符，特別是 BOM 和控制字符
    /// </summary>
    private static string RemoveInvalidCharacters(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        var result = new StringBuilder();
        
        foreach (char c in input)
        {
            // 移除 BOM 和其他控制字符，但保留常用的空白字符
            if (c == '\uFEFF' || // BOM
                (char.IsControl(c) && c != '\n' && c != '\r' && c != '\t') ||
                c > 0xFFFD) // 替換字符之後的字符
            {
                continue;
            }
            
            result.Append(c);
        }
        
        return result.ToString();
    }

    /// <summary>
    /// 修復常見的 JSON 格式問題
    /// </summary>
    private static string RepairCommonJsonIssues(string jsonString)
    {
        if (string.IsNullOrEmpty(jsonString))
            return jsonString;

        var result = jsonString;

        // 修復常見的編碼問題
        result = result
            .Replace("'", "\"")  // 單引號改為雙引號
            .Replace("\u201C", "\"")  // 智能左引號
            .Replace("\u201D", "\"")  // 智能右引號  
            .Replace("\u2018", "\"")  // 智能左單引號
            .Replace("\u2019", "\""); // 智能右單引號

        // 修復尾隨逗號問題（雖然 JsonSerializerOptions 已經處理了）
        result = System.Text.RegularExpressions.Regex.Replace(result, @",\s*([}\]])", "$1");

        // 修復未轉義的引號（簡單處理）
        result = System.Text.RegularExpressions.Regex.Replace(result, @"(?<!\\)""(?=.*"":)", "\\\"");

        return result;
    }

    /// <summary>
    /// 當 JSON 解析完全失敗時的後備場景描述提取
    /// </summary>
    private SceneDescription ExtractFallbackSceneDescription(string response)
    {
        if (string.IsNullOrWhiteSpace(response))
        {
            return new SceneDescription { MainDescription = "無法分析場景" };
        }

        // 嘗試從純文本響應中提取有用信息
        var description = new SceneDescription();
        
        // 使用整個響應作為主要描述，但限制長度
        var cleanResponse = RemoveInvalidCharacters(response.Trim());
        description.MainDescription = cleanResponse.Length > 500 ? 
            cleanResponse.Substring(0, 500) + "..." : 
            cleanResponse;

        // 嘗試提取一些基本信息
        var lowerResponse = cleanResponse.ToLower();
        
        // 檢測餐廳相關關鍵字
        var restaurantKeywords = new[] { "restaurant", "dining", "food", "menu", "cook", "kitchen", "餐廳", "菜單", "烹飪", "廚房", "料理" };
        if (restaurantKeywords.Any(keyword => lowerResponse.Contains(keyword)))
        {
            description.RestaurantCategory = "餐廳";
            description.Setting = "餐廳環境";
        }

        // 檢測情緒相關詞語
        if (lowerResponse.Contains("warm") || lowerResponse.Contains("cozy") || lowerResponse.Contains("溫暖"))
        {
            description.Mood = "溫暖";
        }
        else if (lowerResponse.Contains("bright") || lowerResponse.Contains("明亮"))
        {
            description.Mood = "明亮";
        }

        return description;
    }

    private List<DetectedText> ParseDetectedTexts(string response)
    {
        try
        {
            var jsonStart = response.IndexOf('{');
            var jsonEnd = response.LastIndexOf('}');
            
            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                var jsonString = response.Substring(jsonStart, jsonEnd - jsonStart + 1);
                var parsed = JsonSerializer.Deserialize<JsonElement>(jsonString);
                
                if (parsed.TryGetProperty("detectedTexts", out var textsArray))
                {
                    return textsArray.EnumerateArray().Select(item => new DetectedText
                    {
                        Text = item.TryGetProperty("text", out var text) ? text.GetString() ?? "" : "",
                        Confidence = item.TryGetProperty("confidence", out var conf) ? conf.GetDouble() : 0.5,
                        Language = item.TryGetProperty("language", out var lang) ? lang.GetString() ?? "unknown" : "unknown",
                        Location = item.TryGetProperty("location", out var location) ? location.GetString() : null
                    }).ToList();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse detected texts JSON");
        }

        return [];
    }

    private SubtitleAnalysis ParseSubtitleAnalysis(string response)
    {
        try
        {
            var jsonStart = response.IndexOf('{');
            var jsonEnd = response.LastIndexOf('}');
            
            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                var jsonString = response.Substring(jsonStart, jsonEnd - jsonStart + 1);
                var parsed = JsonSerializer.Deserialize<JsonElement>(jsonString);
                
                return new SubtitleAnalysis
                {
                    Sentiment = parsed.TryGetProperty("sentiment", out var sentiment) ? sentiment.GetString() ?? "neutral" : "neutral",
                    Topics = parsed.TryGetProperty("topics", out var topics) ? 
                        topics.EnumerateArray().Select(t => t.GetString() ?? "").ToList() : [],
                    Keywords = parsed.TryGetProperty("keywords", out var keywords) ? 
                        keywords.EnumerateArray().Select(k => k.GetString() ?? "").ToList() : [],
                    IsDialog = parsed.TryGetProperty("isDialog", out var isDialog) ? isDialog.GetBoolean() : false,
                    Summary = parsed.TryGetProperty("summary", out var summary) ? summary.GetString() ?? "" : ""
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse subtitle analysis JSON, using fallback");
        }

        return new SubtitleAnalysis();
    }

    public async Task<List<string>> ConfirmRestaurantNamesAsync(List<string> restaurantNames, CancellationToken cancellationToken = default)
    {
        try
        {
            if (restaurantNames == null || !restaurantNames.Any())
            {
                return new List<string>();
            }

            var restaurantList = string.Join("\n", restaurantNames.Select((name, index) => $"{index + 1}. {name}"));
            
            var prompt = $@"請分析以下從影片中提取的文字清單，判斷哪些是真正的餐廳名稱。

文字清單：
{restaurantList}

請根據以下標準進行判斷：
1. 明確是餐廳、咖啡店、食堂、小吃店等餐飲場所的名稱
2. 具有中文、日文或韓文餐廳名稱的特徵
3. 不是地址、價格、菜單項目、人名或其他非餐廳相關文字
4. 不是單個字元或過於簡短的文字

請以JSON格式回傳確認的餐廳名稱清單：
{{
    ""confirmed_restaurants"": [
        ""確認的餐廳名稱1"",
        ""確認的餐廳名稱2""
    ]
}}

只回傳JSON，不要額外的解釋。";

            _logger.LogDebug("向AI查詢餐廳名稱確認，共 {Count} 個候選項目", restaurantNames.Count);
            
            var response = await _lmStudioClient.SendTextRequestAsync(prompt, cancellationToken);
            
            _logger.LogDebug("AI回應餐廳名稱確認: {Response}", response);
            
            return ParseAIRestaurantConfirmation(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI餐廳名稱確認失敗");
            // 發生錯誤時，使用基本的規則篩選作為fallback
            return restaurantNames.Where(name => IsLikelyRestaurantName(name, null)).ToList();
        }
    }

    public async Task<List<string>> ExtractRestaurantNamesFromReportAsync(string reportText, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(reportText))
            {
                _logger.LogWarning("報告內容為空，無法提取餐廳名稱");
                return new List<string>();
            }

            var prompt = $@"請仔細分析以下影片分析報告，提取其中提到的所有餐廳、咖啡店、食堂、小吃店等餐飲場所的具體名稱。

報告內容：
{reportText}

提取規則：
1. 重點關注具體的店家名稱，而非一般性的食物類型或描述
2. 包含中文、日文、韓文、英文等各種語言的餐廳名稱
3. 排除一般性描述詞（如「餐廳」、「咖啡店」、「美食」等）
4. 排除食物名稱或菜品（如「拉麵」、「壽司」、「泡菜」等）
5. 排除地址、價格、營業時間等非店名資訊
6. 只提取在報告中明確提及的具體店家名稱

請以JSON格式回傳提取到的餐廳名稱清單：
{{
    ""restaurant_names"": [
        ""具體餐廳名稱1"",
        ""具體餐廳名稱2"",
        ""具體餐廳名稱3""
    ]
}}

如果報告中沒有明確的餐廳名稱，請回傳空陣列。
只回傳JSON，不要額外的解釋。";

            _logger.LogInformation("開始使用 OpenRouter 從報告中提取餐廳名稱，報告長度：{Length} 字元", reportText.Length);
            
            var response = await _openRouterClient.SendTextRequestAsync(prompt, cancellationToken);
            
            _logger.LogDebug("OpenRouter 回應餐廳名稱提取: {Response}", response);
            
            var extractedNames = ParseAIRestaurantExtraction(response);
            
            _logger.LogInformation("從報告中成功提取到 {Count} 個餐廳名稱: {Names}", 
                extractedNames.Count, string.Join(", ", extractedNames));
            
            return extractedNames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "從報告提取餐廳名稱失敗");
            return new List<string>();
        }
    }

    private List<string> ParseAIRestaurantExtraction(string aiResponse)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(aiResponse))
            {
                return new List<string>();
            }

            // 清理回應文字，移除多餘的 markdown 標記或其他格式
            var cleanResponse = aiResponse.Trim();
            if (cleanResponse.StartsWith("```json"))
            {
                cleanResponse = cleanResponse.Substring(7);
            }
            if (cleanResponse.EndsWith("```"))
            {
                cleanResponse = cleanResponse.Substring(0, cleanResponse.Length - 3);
            }
            cleanResponse = cleanResponse.Trim();

            var jsonDoc = JsonDocument.Parse(cleanResponse);
            var restaurantNames = new List<string>();

            if (jsonDoc.RootElement.TryGetProperty("restaurant_names", out var namesElement) && 
                namesElement.ValueKind == JsonValueKind.Array)
            {
                foreach (var nameElement in namesElement.EnumerateArray())
                {
                    if (nameElement.ValueKind == JsonValueKind.String)
                    {
                        var restaurantName = nameElement.GetString();
                        if (!string.IsNullOrWhiteSpace(restaurantName))
                        {
                            restaurantNames.Add(restaurantName.Trim());
                        }
                    }
                }
            }

            return restaurantNames.Where(name => 
                !string.IsNullOrWhiteSpace(name) && 
                name.Length > 1 && 
                name.Length < 100
            ).Distinct().ToList();
        }
        catch (JsonException ex)
        {
            _logger.LogWarning(ex, "解析AI餐廳名稱提取回應JSON失敗，嘗試基本文字解析: {Response}", aiResponse);
            
            // 備用解析：尋找可能的餐廳名稱
            var lines = aiResponse.Split('\n')
                .Where(line => !string.IsNullOrWhiteSpace(line) && 
                             !line.StartsWith("分析結果") && 
                             !line.StartsWith("說明") &&
                             !line.Contains("餐廳名稱") &&
                             !line.Contains("提取"))
                .Select(line => line.Trim().TrimStart('-', '*', '•', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '.', ' ').Trim())
                .Where(line => line.Length > 1 && line.Length < 50)
                .ToList();
                
            return lines.Take(5).ToList(); // 最多返回5個作為備用
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析AI餐廳名稱提取回應失敗: {Response}", aiResponse);
            return new List<string>();
        }
    }

    private List<string> ParseAIRestaurantConfirmation(string response)
    {
        try
        {
            var jsonStart = response.IndexOf('{');
            var jsonEnd = response.LastIndexOf('}');
            
            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                var jsonString = response.Substring(jsonStart, jsonEnd - jsonStart + 1);
                var parsed = JsonSerializer.Deserialize<JsonElement>(jsonString);
                
                if (parsed.TryGetProperty("confirmed_restaurants", out var confirmedRestaurants))
                {
                    var results = confirmedRestaurants.EnumerateArray()
                        .Select(r => r.GetString()?.Trim())
                        .Where(r => !string.IsNullOrWhiteSpace(r))
                        .ToList();
                    
                    _logger.LogDebug("AI確認了 {Count} 個餐廳名稱", results.Count);
                    return results!;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "解析AI餐廳確認回應失敗");
        }

        // 解析失敗時的fallback
        return new List<string>();
    }

    private List<PlaceInfo> ParsePlaceInfos(string response)
    {
        try
        {
            var jsonStart = response.IndexOf('{');
            var jsonEnd = response.LastIndexOf('}');
            
            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                var jsonString = response.Substring(jsonStart, jsonEnd - jsonStart + 1);
                var parsed = JsonSerializer.Deserialize<JsonElement>(jsonString);
                
                if (parsed.TryGetProperty("placeInfos", out var placeArray))
                {
                    return placeArray.EnumerateArray().Select(item => new PlaceInfo
                    {
                        Name = item.TryGetProperty("name", out var name) ? name.GetString() : null,
                        Address = item.TryGetProperty("address", out var address) ? address.GetString() : null,
                        Phone = item.TryGetProperty("phone", out var phone) ? phone.GetString() : null,
                        Website = item.TryGetProperty("website", out var website) ? website.GetString() : null,
                        BusinessHours = item.TryGetProperty("businessHours", out var hours) ? hours.GetString() : null,
                        Category = item.TryGetProperty("category", out var category) ? category.GetString() : null,
                        Description = item.TryGetProperty("description", out var description) ? description.GetString() : null,
                        Confidence = item.TryGetProperty("confidence", out var confidence) ? confidence.GetDouble() : 0.5,
                        OriginalTexts = item.TryGetProperty("originalTexts", out var texts) ? 
                            texts.EnumerateArray().Select(t => t.GetString() ?? "").ToList() : []
                    }).ToList();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse place infos JSON");
        }

        return [];
    }
}