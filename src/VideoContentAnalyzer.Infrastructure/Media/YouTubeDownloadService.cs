using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Text.Json;
using System.Text.RegularExpressions;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;
using VideoContentAnalyzer.Core.Utilities;

namespace VideoContentAnalyzer.Infrastructure.Media;

public class YouTubeDownloadService : IYouTubeDownloadService
{
    private readonly YouTubeDownloadOptions _options;
    private readonly ILogger<YouTubeDownloadService> _logger;
    private readonly IYouTubeApiService? _youtubeApiService;
    private readonly IPlaceDetectionService? _placeDetectionService;
    private readonly string _resolvedOutputDirectory;
    private static readonly Regex YouTubeUrlRegex = new(
        @"(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?|shorts)\/|.*[?&]v=)|youtu\.be\/)([^""&?\/\s]{11})",
        RegexOptions.IgnoreCase | RegexOptions.Compiled);

    public YouTubeDownloadService(
        IOptions<YouTubeDownloadOptions> options,
        ILogger<YouTubeDownloadService> logger,
        IYouTubeApiService? youtubeApiService = null,
        IPlaceDetectionService? placeDetectionService = null)
    {
        _options = options.Value;
        _logger = logger;
        _youtubeApiService = youtubeApiService;
        _placeDetectionService = placeDetectionService;
        _resolvedOutputDirectory = ProjectPathHelper.ResolveProjectPath(_options.OutputDirectory);
    }

    public async Task<YouTubeDownloadResult> DownloadVideoAsync(string urlOrId, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            _logger.LogInformation("開始下載 YouTube 影片和收集元資料: {UrlOrId}", urlOrId);

            // 確保輸出目錄存在
            ProjectPathHelper.EnsureDirectoryExists(_resolvedOutputDirectory);
            _logger.LogDebug("輸出目錄已確保存在: {OutputDirectory}", _resolvedOutputDirectory);

            // 正規化 URL
            var normalizedUrl = NormalizeYouTubeUrl(urlOrId);
            var videoId = ExtractVideoId(normalizedUrl) ?? urlOrId;
            
            // 1. 收集完整的影片元資料
            YouTubeVideoInfo videoInfo;
            if (_youtubeApiService?.IsAvailable() == true)
            {
                _logger.LogInformation("使用 YouTube API 收集完整元資料: {VideoId}", videoId);
                var metadataResult = await _youtubeApiService.CollectVideoMetadataAsync(videoId, cancellationToken);
                
                if (!metadataResult.Success)
                {
                    // 回退到基本資訊收集
                    _logger.LogWarning("完整元資料收集失敗，回退到基本資訊收集: {Error}", metadataResult.ErrorMessage);
                    videoInfo = await GetVideoInfoAsync(normalizedUrl, cancellationToken);
                }
                else
                {
                    videoInfo = metadataResult.VideoInfo;
                    _logger.LogInformation("成功收集完整元資料: {VideoId}, 耗時: {Duration}ms", 
                        videoId, metadataResult.CollectionDuration.TotalMilliseconds);
                }
            }
            else
            {
                // 使用 yt-dlp 取得基本資訊
                videoInfo = await GetVideoInfoAsync(normalizedUrl, cancellationToken);
            }
            
            if (videoInfo == null)
            {
                return new YouTubeDownloadResult
                {
                    Success = false,
                    VideoPath = string.Empty,
                    ErrorMessage = "無法取得影片資訊"
                };
            }

            // 2. 地點檢測（如果啟用且為旅遊/美食相關影片）
            if (_placeDetectionService?.IsEnabled == true && ShouldDetectPlaces(videoInfo))
            {
                _logger.LogInformation("開始檢測影片中的地點資訊: {VideoId}", videoInfo.Id);
                try
                {
                    var detectedPlaces = await _placeDetectionService.DetectPlacesFromVideoInfoAsync(videoInfo, cancellationToken);
                    videoInfo.DetectedPlaces = detectedPlaces;
                    
                    _logger.LogInformation("檢測到 {Count} 個地點", detectedPlaces.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "地點檢測時發生錯誤，繼續進行下載");
                }
            }

            // 3. 根據影片語言資訊決定字幕語言
            string? preferredSubtitleLanguage = null;
            if (_youtubeApiService?.IsAvailable() == true)
            {
                preferredSubtitleLanguage = _youtubeApiService.GetPreferredSubtitleLanguage(videoInfo);
                _logger.LogInformation("根據影片語言選擇字幕語言: {Language}", preferredSubtitleLanguage);
            }

            // 4. 執行影片下載
            var outputPath = Path.Combine(_resolvedOutputDirectory,
                _options.KeepOriginalFilename ? _options.FilenameTemplate : $"{videoInfo.Id}.%(ext)s");

            var arguments = BuildDownloadArguments(normalizedUrl, outputPath, preferredSubtitleLanguage);
            
            _logger.LogInformation("執行 yt-dlp 命令: {Arguments}", string.Join(" ", arguments));
            _logger.LogDebug("工作目錄: {WorkingDirectory}", Directory.GetCurrentDirectory());

            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "yt-dlp",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    // 明確設定工作目錄
                    WorkingDirectory = Directory.GetCurrentDirectory()
                }
            };

            // 使用 ArgumentList 而不是 Arguments 來避免引號問題
            foreach (var arg in arguments)
            {
                process.StartInfo.ArgumentList.Add(arg);
            }

            var output = new List<string>();
            var errors = new List<string>();

            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    output.Add(e.Data);
                    _logger.LogDebug("yt-dlp output: {Data}", e.Data);
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    errors.Add(e.Data);
                    _logger.LogWarning("yt-dlp error: {Data}", e.Data);
                }
            };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            await process.WaitForExitAsync(cancellationToken);

            var downloadDuration = DateTime.UtcNow - startTime;

            _logger.LogInformation("yt-dlp 進程結束 - 退出代碼: {ExitCode}, 輸出行數: {OutputLines}, 錯誤行數: {ErrorLines}", 
                process.ExitCode, output.Count, errors.Count);

            if (process.ExitCode == 0)
            {
                return await HandleSuccessfulDownload(videoInfo, downloadDuration, cancellationToken);
            }
            else
            {
                var errorMessage = string.Join("\n", errors);

                // 檢查是否只是字幕下載失敗，而影片本身下載成功
                var isSubtitleOnlyError = IsSubtitleOnlyError(errors, output);

                if (isSubtitleOnlyError)
                {
                    _logger.LogWarning("字幕下載失敗，但影片下載成功，繼續處理: {Error}", errorMessage);
                    return await HandleSuccessfulDownload(videoInfo, downloadDuration, cancellationToken);
                }

                _logger.LogError("YouTube 影片下載失敗，退出代碼: {ExitCode}, 錯誤: {Error}",
                    process.ExitCode, errorMessage);

                return new YouTubeDownloadResult
                {
                    Success = false,
                    VideoPath = string.Empty,
                    ErrorMessage = $"下載失敗: {errorMessage}"
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YouTube 影片下載過程發生例外");
            
            return new YouTubeDownloadResult
            {
                Success = false,
                VideoPath = string.Empty,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<YouTubeVideoInfo> GetVideoInfoAsync(string urlOrId, CancellationToken cancellationToken = default)
    {
        try
        {
            var videoId = ExtractVideoId(urlOrId);
            
            // 優先使用 YouTube API
            var apiAvailable = _youtubeApiService?.IsAvailable() == true;
            _logger.LogDebug("YouTube API 服務狀態檢查: Available={Available}, VideoId={VideoId}", apiAvailable, videoId);
            
            if (apiAvailable && !string.IsNullOrEmpty(videoId))
            {
                _logger.LogInformation("正在使用 YouTube API 取得影片資訊: {VideoId}", videoId);
                var apiVideoInfo = await _youtubeApiService.GetVideoDetailsAsync(videoId);
                if (apiVideoInfo != null)
                {
                    _logger.LogInformation("✅ 成功透過 YouTube API 取得影片資訊: {Title} (語言: {AudioLang}/{DefaultLang})", 
                        apiVideoInfo.Title, apiVideoInfo.DefaultAudioLanguage, apiVideoInfo.DefaultLanguage);
                    return apiVideoInfo;
                }
                
                _logger.LogWarning("YouTube API 未能取得影片資訊，回退使用 yt-dlp: {VideoId}", videoId);
            }
            
            // 回退到 yt-dlp 方式
            return await GetVideoInfoFromYtDlpAsync(urlOrId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取得 YouTube 影片資訊時發生例外");
            return null!;
        }
    }

    private async Task<YouTubeVideoInfo> GetVideoInfoFromYtDlpAsync(string urlOrId, CancellationToken cancellationToken)
    {
        var normalizedUrl = NormalizeYouTubeUrl(urlOrId);
        
        var process = new Process
        {
            StartInfo = new ProcessStartInfo
            {
                FileName = "yt-dlp",
                Arguments = $"--dump-json --no-download \"{normalizedUrl}\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            }
        };

        process.Start();
        var output = await process.StandardOutput.ReadToEndAsync();
        await process.WaitForExitAsync(cancellationToken);

        if (process.ExitCode == 0 && !string.IsNullOrEmpty(output))
        {
            var jsonData = JsonSerializer.Deserialize<JsonElement>(output);
            return ParseVideoInfo(jsonData);
        }
        else
        {
            var error = await process.StandardError.ReadToEndAsync();
            _logger.LogError("無法透過 yt-dlp 取得 YouTube 影片資訊: {Error}", error);
            return null!;
        }
    }

    public async Task<bool> IsValidYouTubeUrlAsync(string urlOrId)
    {
        if (string.IsNullOrWhiteSpace(urlOrId))
            return false;

        // 檢查是否為 11 位字元的 YouTube ID
        if (urlOrId.Length == 11 && !urlOrId.Contains('/') && !urlOrId.Contains(' '))
            return true;

        // 檢查是否為有效的 YouTube URL
        return await Task.FromResult(YouTubeUrlRegex.IsMatch(urlOrId));
    }

    private string NormalizeYouTubeUrl(string urlOrId)
    {
        // 如果是 11 位字元的 ID，轉換為完整 URL
        if (urlOrId.Length == 11 && !urlOrId.Contains('/'))
        {
            return $"https://www.youtube.com/watch?v={urlOrId}";
        }

        return urlOrId;
    }

    private List<string> BuildDownloadArguments(string url, string outputPath, string? preferredSubtitleLanguage = null)
    {
        var args = new List<string>
        {
            url,
            "-o", outputPath,
            "--no-playlist"
        };

        // 使用與直接 yt-dlp 完全相同的最簡策略
        if (_options.PreferredQuality.ToLower() == "worst")
        {
            args.AddRange(["-f", "worst"]);
        }
        else
        {
            // 完全模擬直接命令: yt-dlp --recode-video mp4 "URL"
            args.AddRange(["--recode-video", "mp4"]);
        }

        // 重新啟用字幕下載功能（已經測試過基本下載可以正常運作）
        if (_options.DownloadSubtitles)
        {
            args.Add("--write-subs");
            args.Add("--write-auto-sub");

            // 優先使用根據影片語言推薦的字幕語言，其次使用配置的語言
            var subtitleLanguages = !string.IsNullOrEmpty(preferredSubtitleLanguage)
                ? $"{preferredSubtitleLanguage},{_options.SubtitleLanguages}"
                : _options.SubtitleLanguages;

            args.AddRange(["--sub-langs", subtitleLanguages]);

            // 添加忽略字幕錯誤的參數，避免字幕下載失敗導致整個下載失敗
            args.Add("--ignore-errors");

            if (_options.EmbedSubtitles)
            {
                args.Add("--embed-subs");
            }
        }

        return args;
    }

    /// <summary>
    /// 檢查是否只是字幕下載失敗，而影片本身下載成功
    /// </summary>
    private bool IsSubtitleOnlyError(List<string> errors, List<string> output)
    {
        // 檢查錯誤訊息是否只包含字幕相關錯誤
        var hasSubtitleError = errors.Any(error =>
            error.Contains("Unable to download video subtitles") ||
            error.Contains("HTTP Error 429: Too Many Requests") ||
            error.Contains("subtitles") && error.Contains("error"));

        // 檢查輸出是否顯示影片下載成功
        var hasVideoDownloadSuccess = output.Any(line =>
            line.Contains("Downloading 1 format(s)") ||
            line.Contains("100%") ||
            line.Contains("Merging formats"));

        // 如果有字幕錯誤但影片下載成功，則認為是字幕專用錯誤
        return hasSubtitleError && hasVideoDownloadSuccess;
    }

    /// <summary>
    /// 測試直接 Shell 執行方式 - 臨時診斷方法
    /// </summary>
    private async Task<bool> TestDirectShellExecution(string url, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("測試直接 Shell 執行 yt-dlp: {Url}", url);
            
            var outputDir = Path.Combine(_resolvedOutputDirectory, "shell_test");
            Directory.CreateDirectory(outputDir);
            
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "/bin/bash",
                    Arguments = $"-c \"cd '{outputDir}' && yt-dlp --recode-video mp4 '{url}'\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            var output = new List<string>();
            var errors = new List<string>();

            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    output.Add(e.Data);
                    _logger.LogDebug("Shell yt-dlp output: {Data}", e.Data);
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    errors.Add(e.Data);
                    _logger.LogDebug("Shell yt-dlp error: {Data}", e.Data);
                }
            };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            await process.WaitForExitAsync(cancellationToken);

            _logger.LogInformation("Shell 執行結果 - 退出代碼: {ExitCode}", process.ExitCode);
            _logger.LogInformation("Shell 執行輸出: {Output}", string.Join("\n", output));
            if (errors.Any())
            {
                _logger.LogWarning("Shell 執行錯誤: {Errors}", string.Join("\n", errors));
            }

            return process.ExitCode == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Shell 執行測試失敗");
            return false;
        }
    }

    
    /// <summary>
    /// 處理成功下載的共同邏輯
    /// </summary>
    private async Task<YouTubeDownloadResult> HandleSuccessfulDownload(
        YouTubeVideoInfo videoInfo, 
        TimeSpan downloadDuration, 
        CancellationToken cancellationToken)
    {
        // 尋找下載的檔案
        var downloadedFile = FindDownloadedFile(videoInfo.Id);
        if (downloadedFile != null && File.Exists(downloadedFile))
        {
            var fileInfo = new FileInfo(downloadedFile);
            
            // 尋找下載的字幕檔案
            var subtitlePaths = FindDownloadedSubtitles(videoInfo.Id);
            var preferredSubtitle = SelectPreferredSubtitle(subtitlePaths);
            
            // 保存元資料到 JSON 檔案
            await SaveMetadataToFileAsync(videoInfo, downloadedFile, cancellationToken);
            
            _logger.LogInformation("YouTube 影片下載成功: {FilePath}, 大小: {Size} bytes, 耗時: {Duration}",
                downloadedFile, fileInfo.Length, downloadDuration);
            
            if (subtitlePaths.Any())
            {
                _logger.LogInformation("找到 {Count} 個字幕檔案: {Subtitles}", 
                    subtitlePaths.Count, string.Join(", ", subtitlePaths.Select(Path.GetFileName)));
            }

            if (videoInfo.DetectedPlaces.Any())
            {
                _logger.LogInformation("檢測到的地點: {Places}", 
                    string.Join(", ", videoInfo.DetectedPlaces.Take(3).Select(p => p.Name)));
            }

            return new YouTubeDownloadResult
            {
                Success = true,
                VideoPath = downloadedFile,
                VideoInfo = videoInfo,
                DownloadDuration = downloadDuration,
                FileSizeBytes = fileInfo.Length,
                SubtitlePaths = subtitlePaths,
                PreferredSubtitlePath = preferredSubtitle
            };
        }
        else
        {
            return new YouTubeDownloadResult
            {
                Success = false,
                VideoPath = string.Empty,
                ErrorMessage = "下載完成但找不到檔案"
            };
        }
    }

    private string? ExtractVideoId(string urlOrId)
    {
        // 如果已經是 11 位字元的 ID
        if (urlOrId.Length == 11 && !urlOrId.Contains('/') && !urlOrId.Contains(' '))
        {
            return urlOrId;
        }

        // 從 URL 中提取 video ID
        var match = YouTubeUrlRegex.Match(urlOrId);
        return match.Success ? match.Groups[1].Value : null;
    }

    private YouTubeVideoInfo ParseVideoInfo(JsonElement json)
    {
        var videoInfo = new YouTubeVideoInfo
        {
            Id = json.GetProperty("id").GetString() ?? "",
            Title = json.GetProperty("title").GetString() ?? "",
            Url = json.GetProperty("webpage_url").GetString() ?? "",
            Description = json.TryGetProperty("description", out var desc) ? desc.GetString() : null,
            Channel = json.TryGetProperty("channel", out var channel) ? channel.GetString() : null,
            Duration = TimeSpan.FromSeconds(json.TryGetProperty("duration", out var duration) ? duration.GetDouble() : 0),
            ThumbnailUrl = json.TryGetProperty("thumbnail", out var thumb) ? thumb.GetString() : null,
            ViewCount = json.TryGetProperty("view_count", out var views) ? views.GetInt64() : null
        };

        // 解析上傳日期
        if (json.TryGetProperty("upload_date", out var uploadDate))
        {
            var dateStr = uploadDate.GetString();
            if (DateTime.TryParseExact(dateStr, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var date))
            {
                videoInfo.UploadDate = date;
            }
        }

        // 解析可用格式
        if (json.TryGetProperty("formats", out var formats))
        {
            foreach (var format in formats.EnumerateArray())
            {
                videoInfo.AvailableFormats.Add(new YouTubeFormat
                {
                    FormatId = format.TryGetProperty("format_id", out var fid) ? fid.GetString() ?? "" : "",
                    Extension = format.TryGetProperty("ext", out var ext) ? ext.GetString() ?? "" : "",
                    Resolution = format.TryGetProperty("resolution", out var res) ? res.GetString() : null,
                    FileSizeBytes = format.TryGetProperty("filesize", out var size) ? 
                        (size.ValueKind == JsonValueKind.Number ? size.GetInt64() : null) : null,
                    VideoCodec = format.TryGetProperty("vcodec", out var vcodec) ? vcodec.GetString() : null,
                    AudioCodec = format.TryGetProperty("acodec", out var acodec) ? acodec.GetString() : null,
                    Fps = format.TryGetProperty("fps", out var fps) ? 
                        (fps.ValueKind == JsonValueKind.Number ? 
                            (int)Math.Round(fps.GetDouble()) : null) : null
                });
            }
        }

        return videoInfo;
    }

    private string? FindDownloadedFile(string videoId)
    {
        var searchPatterns = new[]
        {
            $"{videoId}.mp4",
            $"{videoId}.webm",
            $"{videoId}.mkv",
            $"*{videoId}*.mp4",
            $"*{videoId}*.webm",
            $"*{videoId}*.mkv"
        };

        foreach (var pattern in searchPatterns)
        {
            var files = Directory.GetFiles(_resolvedOutputDirectory, pattern, SearchOption.TopDirectoryOnly);
            if (files.Length > 0)
            {
                return files.OrderByDescending(f => new FileInfo(f).CreationTime).First();
            }
        }

        return null;
    }

    private List<string> FindDownloadedSubtitles(string videoId)
    {
        var subtitleFiles = new List<string>();
        
        // yt-dlp 字幕檔案的常見格式
        var subtitleExtensions = new[] { ".srt", ".vtt", ".ass", ".ssa" };
        var languageCodes = _options.SubtitleLanguages.Split(',').Select(l => l.Trim()).ToArray();
        
        // 搜尋模式：videoId.language.extension 或 videoId.extension
        var searchPatterns = new List<string>();
        
        foreach (var ext in subtitleExtensions)
        {
            // 直接模式：videoId.srt
            searchPatterns.Add($"{videoId}{ext}");
            searchPatterns.Add($"*{videoId}*{ext}");
            
            // 語言模式：videoId.zh.srt, videoId.zh-TW.srt 等
            foreach (var lang in languageCodes)
            {
                searchPatterns.Add($"{videoId}.{lang}{ext}");
                searchPatterns.Add($"*{videoId}*.{lang}{ext}");
                searchPatterns.Add($"{videoId}.{lang}-*{ext}");
                searchPatterns.Add($"*{videoId}*.{lang}-*{ext}");
            }
        }

        foreach (var pattern in searchPatterns)
        {
            try
            {
                var files = Directory.GetFiles(_resolvedOutputDirectory, pattern, SearchOption.TopDirectoryOnly);
                subtitleFiles.AddRange(files);
            }
            catch (Exception ex)
            {
                _logger.LogDebug("搜尋字幕檔案時發生錯誤: {Pattern}, {Error}", pattern, ex.Message);
            }
        }

        // 去除重複並排序
        return subtitleFiles.Distinct().OrderBy(f => f).ToList();
    }

    private bool ShouldDetectPlaces(YouTubeVideoInfo videoInfo)
    {
        // 根據影片分類和內容判斷是否需要檢測地點
        var travelFoodCategories = new[] { "旅遊", "美食", "Travel", "Food", "Howto & Style" };
        
        if (!string.IsNullOrEmpty(videoInfo.CategoryName) && 
            travelFoodCategories.Any(cat => videoInfo.CategoryName.Contains(cat, StringComparison.OrdinalIgnoreCase)))
        {
            return true;
        }

        // 根據標題和描述中的關鍵字判斷
        var travelFoodKeywords = new[]
        {
            "旅遊", "旅行", "美食", "餐廳", "咖啡廳", "小吃", "夜市", "景點", "飯店", "民宿",
            "travel", "trip", "food", "restaurant", "cafe", "hotel", "sightseeing", "tourism"
        };

        var titleAndDesc = $"{videoInfo.Title} {videoInfo.Description}".ToLower();
        return travelFoodKeywords.Any(keyword => titleAndDesc.Contains(keyword.ToLower()));
    }

    private async Task SaveMetadataToFileAsync(YouTubeVideoInfo videoInfo, string videoPath, CancellationToken cancellationToken)
    {
        try
        {
            var metadataPath = Path.ChangeExtension(videoPath, ".metadata.json");
            var metadataJson = JsonSerializer.Serialize(videoInfo, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
            
            await File.WriteAllTextAsync(metadataPath, metadataJson, cancellationToken);
            _logger.LogDebug("元資料已保存至: {MetadataPath}", metadataPath);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "保存元資料檔案時發生錯誤");
        }
    }

    private string? SelectPreferredSubtitle(List<string> subtitlePaths)
    {
        if (!subtitlePaths.Any())
            return null;

        var languagePreferences = _options.SubtitleLanguages.Split(',').Select(l => l.Trim()).ToArray();
        
        // 優先選擇偏好語言的字幕
        foreach (var lang in languagePreferences)
        {
            var preferredSubtitle = subtitlePaths.FirstOrDefault(path => 
                Path.GetFileNameWithoutExtension(path).Contains($".{lang}") ||
                Path.GetFileNameWithoutExtension(path).EndsWith($".{lang}"));
                
            if (preferredSubtitle != null)
            {
                _logger.LogDebug("選擇偏好字幕檔案: {Path} (語言: {Language})", preferredSubtitle, lang);
                return preferredSubtitle;
            }
        }

        // 如果沒有找到偏好語言，選擇第一個
        var firstSubtitle = subtitlePaths.First();
        _logger.LogDebug("選擇預設字幕檔案: {Path}", firstSubtitle);
        return firstSubtitle;
    }
}