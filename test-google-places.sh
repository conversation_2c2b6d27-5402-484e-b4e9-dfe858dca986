#!/bin/bash

# Google Places API 測試腳本
# 用法: ./test-google-places.sh "餐廳名稱"

# 設置顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 檢查參數
if [ $# -eq 0 ]; then
    echo -e "${RED}錯誤: 請提供要查詢的餐廳名稱${NC}"
    echo -e "${YELLOW}用法: $0 \"餐廳名稱\"${NC}"
    echo -e "${YELLOW}範例: $0 \"京町家\"${NC}"
    exit 1
fi

QUERY="$1"

# API 金鑰設置
if [ -z "$GooglePlaces__ApiKey" ] && [ -z "$GOOGLE_PLACES_API_KEY" ]; then
    # 如果環境變數沒設置，使用預設的 API 金鑰
    API_KEY="AIzaSyChwHQ7Lg5mSNUPgKbIEHAlH1AXzZuwSJE"
    echo -e "${YELLOW}警告: 使用內建的 API 金鑰${NC}"
else
    # 優先使用 GooglePlaces__ApiKey，其次使用 GOOGLE_PLACES_API_KEY
    API_KEY="${GooglePlaces__ApiKey:-$GOOGLE_PLACES_API_KEY}"
    echo -e "${GREEN}使用環境變數中的 API 金鑰${NC}"
fi

echo -e "${BLUE}=== Google Places API 測試工具 ===${NC}"
echo -e "${CYAN}查詢餐廳: ${QUERY}${NC}"
echo -e "${CYAN}API 金鑰: ${API_KEY:0:20}...${NC}"
echo ""

# URL 編碼查詢字串
ENCODED_QUERY=$(python3 -c "import urllib.parse; print(urllib.parse.quote('$QUERY'))")

# 構建 API 請求 URL
URL="https://maps.googleapis.com/maps/api/place/textsearch/json?query=${ENCODED_QUERY}&key=${API_KEY}"

echo -e "${PURPLE}發送請求到 Google Places API...${NC}"
echo -e "${YELLOW}請求 URL: ${URL}${NC}"
echo ""

# 發送請求並保存響應
RESPONSE=$(curl -s "$URL")
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$URL")

echo -e "${BLUE}HTTP 狀態碼: ${HTTP_STATUS}${NC}"
echo ""

# 檢查是否成功
if [ "$HTTP_STATUS" != "200" ]; then
    echo -e "${RED}API 請求失敗，HTTP 狀態碼: $HTTP_STATUS${NC}"
    echo -e "${RED}響應內容:${NC}"
    echo "$RESPONSE"
    exit 1
fi

# 檢查是否安裝了 jq
if command -v jq &> /dev/null; then
    echo -e "${GREEN}=== 格式化的 JSON 響應 ===${NC}"
    echo "$RESPONSE" | jq .
    echo ""
    
    # 解析並顯示結果摘要
    STATUS=$(echo "$RESPONSE" | jq -r '.status // "UNKNOWN"')
    RESULT_COUNT=$(echo "$RESPONSE" | jq '.results | length // 0')
    
    echo -e "${BLUE}=== 結果摘要 ===${NC}"
    echo -e "${CYAN}狀態: ${STATUS}${NC}"
    echo -e "${CYAN}找到的地點數量: ${RESULT_COUNT}${NC}"
    echo ""
    
    if [ "$STATUS" = "OK" ] && [ "$RESULT_COUNT" -gt 0 ]; then
        echo -e "${GREEN}=== 地點詳細資訊 ===${NC}"
        echo "$RESPONSE" | jq -r '.results[] | 
            "地點名稱: " + (.name // "未知") + "\n" +
            "地址: " + (.formatted_address // "未提供") + "\n" +
            "評分: " + ((.rating // 0) | tostring) + "/5" + "\n" +
            "價格等級: " + ((.price_level // "未知") | tostring) + "\n" +
            "Place ID: " + (.place_id // "未知") + "\n" +
            "類型: " + ((.types // []) | join(", ")) + "\n" +
            "營業狀態: " + (if .business_status then .business_status else "未知" end) + "\n" +
            "------------------------"'
    elif [ "$STATUS" = "ZERO_RESULTS" ]; then
        echo -e "${YELLOW}沒有找到符合查詢條件的地點${NC}"
    else
        echo -e "${RED}API 返回錯誤狀態: ${STATUS}${NC}"
        ERROR_MESSAGE=$(echo "$RESPONSE" | jq -r '.error_message // "無錯誤訊息"')
        echo -e "${RED}錯誤訊息: ${ERROR_MESSAGE}${NC}"
    fi
else
    echo -e "${YELLOW}未安裝 jq，顯示原始 JSON 響應:${NC}"
    echo "$RESPONSE"
    echo ""
    echo -e "${YELLOW}提示: 安裝 jq 可以獲得更好的格式化輸出${NC}"
    echo -e "${YELLOW}macOS: brew install jq${NC}"
    echo -e "${YELLOW}Ubuntu: sudo apt install jq${NC}"
fi

echo ""
echo -e "${GREEN}=== 測試完成 ===${NC}"